import base64
import aiofiles
import httpx
from typing import List, Dict, Any, Optional, Union, Tuple
from nonebot.adapters import Message
from nonebot.adapters.onebot.v11 import MessageSegment as V11MS
from nonebot.adapters.onebot.v11 import Message as V11Msg

from .data_manager import data_manager

async def build_message(content: str, image_ids: List[str] = None) -> Message:
    """构建消息
    
    Args:
        content: 消息内容
        image_ids: 图片ID列表
    
    Returns:
        构建好的消息对象
    """
    message = V11Msg()
    
    # 处理内容中可能包含的表情、at等特殊段
    # 如果内容中包含CQ码，则解析并添加相应的消息段
    if content:
        # 检查内容是否包含CQ码
        if "[CQ:" in content:
            from nonebot.adapters.onebot.v11.message import Message as V11Message
            try:
                # 尝试直接解析整个字符串为消息对象，这样会自动处理CQ码
                message = V11Message(content)
            except Exception as e:
                # 解析失败的情况下，尝试提取CQ码部分并单独处理
                try:
                    import re
                    
                    # 定义CQ码匹配模式
                    cq_pattern = r'\[CQ:([^,\]]+)(?:,([^\]]+))?\]'
                    
                    # 提取所有匹配项
                    matches = list(re.finditer(cq_pattern, content))
                    
                    # 如果没有匹配项，作为普通文本处理
                    if not matches:
                        message.append(V11MS.text(content))
                    else:
                        # 分段处理
                        last_end = 0
                        for match in matches:
                            # 添加CQ码前的文本
                            if match.start() > last_end:
                                text_segment = content[last_end:match.start()]
                                if text_segment:
                                    message.append(V11MS.text(text_segment))
                            
                            # 提取CQ码类型和参数
                            cq_type = match.group(1)
                            cq_params_str = match.group(2) or ""
                            
                            # 解析参数
                            cq_params = {}
                            if cq_params_str:
                                for param in cq_params_str.split(','):
                                    if '=' in param:
                                        key, value = param.split('=', 1)
                                        cq_params[key] = value
                            
                            # 根据类型添加对应的消息段
                            try:
                                if cq_type == "text":
                                    message.append(V11MS.text(cq_params.get("text", "")))
                                elif cq_type == "face":
                                    message.append(V11MS.face(cq_params.get("id", "0")))
                                elif cq_type == "at":
                                    message.append(V11MS.at(cq_params.get("qq", "all")))
                                elif cq_type == "image":
                                    message.append(V11MS.image(cq_params.get("file", "")))
                                elif cq_type == "reply":
                                    message.append(V11MS.reply(cq_params.get("id", "")))
                                elif cq_type == "poke":
                                    message.append(V11MS.poke(cq_params.get("qq", "")))
                                elif cq_type == "xml":
                                    message.append(V11MS.xml(cq_params.get("data", "")))
                                elif cq_type == "json":
                                    message.append(V11MS.json(cq_params.get("data", "")))
                                else:
                                    # 对于未知类型，直接保留原始CQ码
                                    message.append(V11MS.text(match.group(0)))
                            except Exception as e:
                                # 如果特定CQ码处理失败，保留原始文本
                                message.append(V11MS.text(match.group(0)))
                            
                            last_end = match.end()
                        
                        # 添加最后一个CQ码之后的文本
                        if last_end < len(content):
                            text_segment = content[last_end:]
                            if text_segment:
                                message.append(V11MS.text(text_segment))
                except Exception as inner_e:
                    # 如果解析过程中出现任何错误，回退到纯文本处理
                    message.append(V11MS.text(content))
        else:
            # 普通文本
            message.append(V11MS.text(content))
    
    # 添加图片
    if image_ids:
        for image_id in image_ids:
            image_path = await data_manager.get_image_path(image_id)
            if image_path:
                # 从文件加载图片
                message.append(V11MS.image(file=f"file:///{image_path.absolute()}"))
    
    return message

async def extract_images_from_message(message: Message) -> List[Tuple[bytes, str]]:
    """从消息中提取图片
    
    Args:
        message: 消息对象
    
    Returns:
        图片数据和文件名列表
    """
    images = []
    
    for segment in message:
        if segment.type == "image":
            # 适配 OneBot V11 的图片格式
            if hasattr(segment, "data") and "url" in segment.data:
                try:
                    # 下载图片
                    async with httpx.AsyncClient() as client:
                        resp = await client.get(segment.data["url"])
                        if resp.status_code == 200:
                            # 提取文件名或生成默认文件名
                            filename = segment.data.get("file", "image.jpg")
                            if "." not in filename:
                                filename += ".jpg"
                            
                            images.append((resp.content, filename))
                except Exception as e:
                    continue
            # 处理 base64 格式图片
            elif hasattr(segment, "data") and "file" in segment.data and segment.data["file"].startswith("base64://"):
                try:
                    # 解析 base64 数据
                    base64_data = segment.data["file"][9:]  # 去掉 "base64://"
                    image_data = base64.b64decode(base64_data)
                    images.append((image_data, "image.jpg"))
                except Exception as e:
                    continue
    
    return images

def extract_faces_from_message(message: Message) -> List[str]:
    """从消息中提取表情
    
    Args:
        message: 消息对象
    
    Returns:
        表情段列表
    """
    faces = []
    
    for segment in message:
        if segment.type == "face" and hasattr(segment, "data") and "id" in segment.data:
            faces.append(str(segment))  # Return full CQ code instead of just the ID
    
    return faces

async def build_complex_message(content: str, data: Dict[str, Any] = None) -> Message:
    """构建复杂消息，支持多种消息类型
    
    Args:
        content: 消息内容
        data: 附加数据，可以包含图片ID、音频URL等
    
    Returns:
        构建好的消息对象
    """
    message = V11Msg()
    
    # 添加文本内容
    if content:
        message.append(V11MS.text(content))
    
    # 处理附加数据
    if data:
        # 处理图片
        if "image_ids" in data and data["image_ids"]:
            for image_id in data["image_ids"]:
                image_path = await data_manager.get_image_path(image_id)
                if image_path:
                    message.append(V11MS.image(file=f"file:///{image_path.absolute()}"))
        
        # 处理音频
        if "audio_url" in data and data["audio_url"]:
            message.append(V11MS.record(file=data["audio_url"]))
        
        # 处理视频
        if "video_url" in data and data["video_url"]:
            message.append(V11MS.video(file=data["video_url"]))
        
        # 处理@
        if "at_qq" in data and data["at_qq"]:
            message.append(V11MS.at(data["at_qq"]))
        
        # 处理回复
        if "reply_id" in data and data["reply_id"]:
            message.append(V11MS.reply(data["reply_id"]))
    
    return message

async def download_media_from_url(url: str) -> Optional[bytes]:
    """从URL下载媒体文件
    
    Args:
        url: 媒体文件URL
    
    Returns:
        媒体文件数据
    """
    try:
        async with httpx.AsyncClient() as client:
            resp = await client.get(url, timeout=10.0)
            if resp.status_code == 200:
                return resp.content
    except Exception as e:
        import logging
        logger = logging.getLogger("custom_reply")
        logger.error(f"下载媒体文件失败: {str(e)}")
    
    return None

def build_xml_message(xml_content: str) -> V11MS:
    """构建XML消息
    
    Args:
        xml_content: XML内容
    
    Returns:
        XML消息段
    """
    return V11MS.xml(data=xml_content)

def build_json_message(json_content: str) -> V11MS:
    """构建JSON消息
    
    Args:
        json_content: JSON内容
    
    Returns:
        JSON消息段
    """
    return V11MS.json(data=json_content)

def build_at_message(qq: Union[int, str]) -> V11MS:
    """构建@消息
    
    Args:
        qq: 要@的QQ号
    
    Returns:
        @消息段
    """
    return V11MS.at(qq)

def build_at_all_message() -> V11MS:
    """构建@全体成员消息
    
    Returns:
        @全体成员消息段
    """
    return V11MS.at("all") 