from nonebot import on_command, on_notice, on_request, on_message
from nonebot.adapters.onebot.v11 import (
    <PERSON><PERSON>,
    GroupMessageEvent,
    GroupRequestEvent,
    GroupDecreaseNoticeEvent,
    GroupIncreaseNoticeEvent,
    NoticeEvent,  # Added NoticeEvent
    MessageSegment,
    Message,
    GROUP_ADMIN,
    GROUP_OWNER
)
from nonebot.exception import FinishedException
from nonebot.permission import SUPERUSER
from nonebot.params import CommandArg
from nonebot.log import logger
from pathlib import Path
import sqlite3
import asyncio
import datetime
import re
from nonebot.rule import Rule
from nonebot_plugin_apscheduler import scheduler

# 自定义规则：排除以#开头的命令消息
def exclude_command() -> Rule:
    async def _exclude_command(event: GroupMessageEvent) -> bool:
        return not event.raw_message.lstrip().startswith("#")
    return Rule(_exclude_command)

# 自定义规则：只处理确认和取消消息
def only_confirm_messages() -> Rule:
    async def _only_confirm_messages(event: GroupMessageEvent) -> bool:
        msg = event.raw_message.strip()
        return msg in ["确认还原配置", "取消还原配置", "确认还原黑名单", "取消还原黑名单", "确认清空记录", "取消清空记录"]
    return Rule(_only_confirm_messages)

# 自定义规则：只处理特定消息
def specific_message(msg_text: str) -> Rule:
    async def _specific_message(event: GroupMessageEvent) -> bool:
        return event.raw_message.strip() == msg_text
    return Rule(_specific_message)

# 自定义规则：只处理退群事件
def is_group_decrease_event() -> Rule:
    async def _is_group_decrease_event(event: NoticeEvent) -> bool:
        return event.notice_type == "group_decrease"
    return Rule(_is_group_decrease_event)

# 自定义规则：只处理入群事件
def is_group_increase_event() -> Rule:
    async def _is_group_increase_event(event: NoticeEvent) -> bool:
        return event.notice_type == "group_increase"
    return Rule(_is_group_increase_event)

# 定义权限
ADMIN_PERMISSION = GROUP_ADMIN | GROUP_OWNER | SUPERUSER

# 创建数据目录
plugin_dir = Path(__file__).parent
data_dir = plugin_dir / "data"
data_dir.mkdir(parents=True, exist_ok=True)
img_dir = plugin_dir / "img"
img_dir.mkdir(parents=True, exist_ok=True)

# 初始化数据库
def init_db():
    db_path = data_dir / "group_manager.db"
    conn = sqlite3.connect(str(db_path))
    cursor = conn.cursor()
    
    # 创建配置表
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS group_config (
        group_id INTEGER PRIMARY KEY,
        welcome_msg TEXT,
        leave_msg TEXT DEFAULT '{avatar}\n{nickname} {user_id}\n退出了群聊，已自动加入黑名单OVO ~',
        auto_approve INTEGER DEFAULT 0,
        plugin_enabled INTEGER DEFAULT 0,
        min_qq_level INTEGER DEFAULT 0,
        private_chat INTEGER DEFAULT 0,
        share_from INTEGER DEFAULT 0,
        share_type TEXT DEFAULT NULL,
        short_welcome_msg TEXT DEFAULT '欢迎 {at} 加入本群！\n添加机器人为好友后发送【群提示】可获取详细信息~',
        not_friend_tip TEXT DEFAULT '{at} 您还不是机器人的好友，请先添加好友后再发送【群提示】获取详细信息~',
        allow_when_no_level INTEGER DEFAULT 1,
        cluster_id INTEGER DEFAULT NULL
    )
    ''')

    # 创建多类型共享配置表
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS group_shares (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        group_id INTEGER,
        share_type TEXT,
        share_from INTEGER,
        created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(group_id, share_type)
    )
    ''')
    
    # 创建黑名单表
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS blacklist (
        group_id INTEGER,
        user_id INTEGER,
        reason TEXT,
        add_time TIMESTAMP,
        PRIMARY KEY (group_id, user_id)
    )
    ''')
    
    # 创建拒绝记录表
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS reject_history (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        group_id INTEGER,
        user_id INTEGER,
        reason TEXT,
        reject_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        comment TEXT
    )
    ''')
    
    # 创建组信息表
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS group_clusters (
        cluster_id INTEGER PRIMARY KEY AUTOINCREMENT,
        cluster_name TEXT UNIQUE NOT NULL,
        description TEXT,
        created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        created_by INTEGER,
        is_active INTEGER DEFAULT 1
    )
    ''')
    
    # 创建用户组内状态跟踪表
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS cluster_user_tracking (
        cluster_id INTEGER,
        user_id INTEGER,
        group_id INTEGER,
        join_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        status TEXT DEFAULT 'active',
        PRIMARY KEY (cluster_id, user_id),
        FOREIGN KEY (cluster_id) REFERENCES group_clusters(cluster_id),
        FOREIGN KEY (group_id) REFERENCES group_config(group_id)
    )
    ''')
    
    # 检查是否需要添加 allow_when_no_level 列
    cursor.execute("PRAGMA table_info(group_config)")
    columns = [column[1] for column in cursor.fetchall()]
    if "allow_when_no_level" not in columns:
        cursor.execute("ALTER TABLE group_config ADD COLUMN allow_when_no_level INTEGER DEFAULT 1")
    
    # 检查是否需要添加 cluster_id 列
    if "cluster_id" not in columns:
        cursor.execute("ALTER TABLE group_config ADD COLUMN cluster_id INTEGER DEFAULT NULL")

    # 创建违规词管理表
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS banned_words (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        group_id INTEGER,
        word TEXT NOT NULL,
        action TEXT DEFAULT 'recall',
        punishment_type TEXT DEFAULT 'mute',
        punishment_duration INTEGER DEFAULT 60,
        violation_threshold INTEGER DEFAULT 3,
        created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        created_by INTEGER,
        UNIQUE(group_id, word)
    )
    ''')

    # 检查并添加新字段到违规词表
    cursor.execute("PRAGMA table_info(banned_words)")
    banned_words_columns = [column[1] for column in cursor.fetchall()]
    if "punishment_type" not in banned_words_columns:
        cursor.execute("ALTER TABLE banned_words ADD COLUMN punishment_type TEXT DEFAULT 'mute'")
    if "punishment_duration" not in banned_words_columns:
        cursor.execute("ALTER TABLE banned_words ADD COLUMN punishment_duration INTEGER DEFAULT 60")
    if "violation_threshold" not in banned_words_columns:
        cursor.execute("ALTER TABLE banned_words ADD COLUMN violation_threshold INTEGER DEFAULT 3")

    # 创建违规记录表
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS violation_records (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        group_id INTEGER,
        user_id INTEGER,
        message_id INTEGER,
        violation_word TEXT,
        original_message TEXT,
        action_taken TEXT,
        violation_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
    ''')

    # 创建违规计数表
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS violation_counts (
        group_id INTEGER,
        user_id INTEGER,
        violation_count INTEGER DEFAULT 0,
        last_violation_time TIMESTAMP,
        PRIMARY KEY (group_id, user_id)
    )
    ''')

    # 检查是否需要添加发言管理相关配置列
    if "speech_monitor_enabled" not in columns:
        cursor.execute("ALTER TABLE group_config ADD COLUMN speech_monitor_enabled INTEGER DEFAULT 0")
    if "violation_threshold" not in columns:
        cursor.execute("ALTER TABLE group_config ADD COLUMN violation_threshold INTEGER DEFAULT 3")
    if "punishment_type" not in columns:
        cursor.execute("ALTER TABLE group_config ADD COLUMN punishment_type TEXT DEFAULT 'mute'")
    if "punishment_duration" not in columns:
        cursor.execute("ALTER TABLE group_config ADD COLUMN punishment_duration INTEGER DEFAULT 60")

    conn.commit()
    conn.close()

init_db()

# 加载配置
def load_config(group_id: int):
    db_path = data_dir / "group_manager.db"
    conn = sqlite3.connect(str(db_path))
    cursor = conn.cursor()
    
    # 查询配置
    cursor.execute("SELECT * FROM group_config WHERE group_id = ?", (group_id,))
    result = cursor.fetchone()
    
    if not result:
        # 如果没有配置，创建默认配置
        cursor.execute(
            "INSERT INTO group_config (group_id, welcome_msg, leave_msg, auto_approve, plugin_enabled, min_qq_level, private_chat, short_welcome_msg, not_friend_tip, allow_when_no_level) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
            (
                group_id,
                "{avatar}\n欢迎 {at} 加入本群！",
                "{avatar}\n{nickname} {user_id}\n退出了群聊，已自动加入黑名单OVO ~",
                0, 0, 0, 0,
                "欢迎 {at} 加入本群！\n添加机器人为好友后发送【群提示】可获取详细信息~",
                "{at} 您还不是机器人的好友，请先添加好友后再发送【群提示】获取详细信息~",
                1  # 默认允许
            )
        )
        conn.commit()
        config = {
            "group_id": group_id,
            "welcome_msg": "{avatar}\n欢迎 {at} 加入本群！",
            "leave_msg": "{avatar}\n{nickname} {user_id}\n退出了群聊，已自动加入黑名单OVO ~",
            "auto_approve": 0,
            "plugin_enabled": 0,
            "min_qq_level": 0,
            "private_chat": 0,
            "share_from": 0,
            "share_type": None,
            "short_welcome_msg": "欢迎 {at} 加入本群！\n添加机器人为好友后发送【群提示】可获取详细信息~",
            "not_friend_tip": "{at} 您还不是机器人的好友，请先添加好友后再发送【群提示】获取详细信息~",
            "allow_when_no_level": 1,  # 默认允许
            "speech_monitor_enabled": 0,
            "violation_threshold": 3,
            "punishment_type": "mute",
            "punishment_duration": 60
        }
    else:
        # 检查是否共享配置
        if result[7] and result[8] == "配置":  # share_from 和 share_type
            # 加载共享的配置
            cursor.execute("SELECT * FROM group_config WHERE group_id = ?", (result[7],))
            shared_config = cursor.fetchone()
            if shared_config:
                # 获取allow_when_no_level值，优先从共享配置获取，如果不存在则默认为1
                allow_when_no_level = shared_config[11] if len(shared_config) > 11 else 1
                # 获取发言监控相关配置
                speech_monitor_enabled = shared_config[12] if len(shared_config) > 12 else 0
                violation_threshold = shared_config[13] if len(shared_config) > 13 else 3
                punishment_type = shared_config[14] if len(shared_config) > 14 else "mute"
                punishment_duration = shared_config[15] if len(shared_config) > 15 else 60

                config = {
                    "group_id": group_id,
                    "welcome_msg": shared_config[1],
                    "leave_msg": shared_config[2],
                    "auto_approve": shared_config[3],
                    "plugin_enabled": shared_config[4],
                    "min_qq_level": shared_config[5],
                    "private_chat": shared_config[6],
                    "share_from": result[7],
                    "share_type": result[8],
                    "short_welcome_msg": shared_config[9],
                    "not_friend_tip": shared_config[10],
                    "allow_when_no_level": allow_when_no_level,
                    "speech_monitor_enabled": speech_monitor_enabled,
                    "violation_threshold": violation_threshold,
                    "punishment_type": punishment_type,
                    "punishment_duration": punishment_duration
                }
            else:
                # 如果共享的配置不存在，重置共享状态
                cursor.execute(
                    "UPDATE group_config SET share_from = 0, share_type = NULL WHERE group_id = ?",
                    (group_id,)
                )
                conn.commit()
                # 从本地配置获取allow_when_no_level值，如果不存在则默认为1
                allow_when_no_level = result[11] if len(result) > 11 else 1
                # 获取发言监控相关配置
                speech_monitor_enabled = result[12] if len(result) > 12 else 0
                violation_threshold = result[13] if len(result) > 13 else 3
                punishment_type = result[14] if len(result) > 14 else "mute"
                punishment_duration = result[15] if len(result) > 15 else 60

                config = {
                    "group_id": result[0],
                    "welcome_msg": result[1],
                    "leave_msg": result[2],
                    "auto_approve": result[3],
                    "plugin_enabled": result[4],
                    "min_qq_level": result[5],
                    "private_chat": result[6],
                    "share_from": 0,
                    "share_type": None,
                    "short_welcome_msg": result[9],
                    "not_friend_tip": result[10],
                    "allow_when_no_level": allow_when_no_level,
                    "speech_monitor_enabled": speech_monitor_enabled,
                    "violation_threshold": violation_threshold,
                    "punishment_type": punishment_type,
                    "punishment_duration": punishment_duration
                }
        else:
            # 使用自己的配置
            # 从本地配置获取allow_when_no_level值，如果不存在则默认为1
            allow_when_no_level = result[11] if len(result) > 11 else 1
            # 获取发言监控相关配置
            speech_monitor_enabled = result[12] if len(result) > 12 else 0
            violation_threshold = result[13] if len(result) > 13 else 3
            punishment_type = result[14] if len(result) > 14 else "mute"
            punishment_duration = result[15] if len(result) > 15 else 60

            config = {
                "group_id": result[0],
                "welcome_msg": result[1],
                "leave_msg": result[2],
                "auto_approve": result[3],
                "plugin_enabled": result[4],
                "min_qq_level": result[5],
                "private_chat": result[6],
                "share_from": result[7],
                "share_type": result[8],
                "short_welcome_msg": result[9],
                "not_friend_tip": result[10],
                "allow_when_no_level": allow_when_no_level,
                "speech_monitor_enabled": speech_monitor_enabled,
                "violation_threshold": violation_threshold,
                "punishment_type": punishment_type,
                "punishment_duration": punishment_duration
            }
    
    conn.close()
    return config

# 获取有效的黑名单来源群ID
def get_effective_blacklist_group_id(current_group_id: int, conn: sqlite3.Connection) -> int:
    cursor = conn.cursor()
    cursor.execute("SELECT share_from, share_type FROM group_config WHERE group_id = ?", (current_group_id,))
    config_row = cursor.fetchone()

    if config_row:
        share_from, share_type = config_row[0], config_row[1]
        if share_from and share_type == "黑名单":
            cursor.execute("SELECT 1 FROM group_config WHERE group_id = ?", (share_from,))
            if cursor.fetchone():
                return share_from
            else:
                logger.warning(f"群 {current_group_id} 的黑名单共享源群 {share_from} 不存在配置中，已自动重置其共享状态。")
                cursor.execute("UPDATE group_config SET share_from = 0, share_type = NULL WHERE group_id = ? AND share_type = '黑名单'", (current_group_id,))
                conn.commit() # 直接提交更改
                return current_group_id 
    return current_group_id

# 检查插件是否启用
def is_plugin_enabled(group_id: int) -> bool:
    config = load_config(group_id)
    return bool(config["plugin_enabled"])

# ===== 组管理相关辅助函数 =====

# 创建组
def do_create_cluster(cluster_name: str, description: str, created_by: int) -> tuple[bool, str]:
    """创建新的组
    
    Args:
        cluster_name: 组名
        description: 组描述
        created_by: 创建者QQ号
        
    Returns:
        tuple[bool, str]: (是否成功, 消息)
    """
    db_path = data_dir / "group_manager.db"
    conn = sqlite3.connect(str(db_path))
    cursor = conn.cursor()
    
    try:
        # 检查组名是否已存在
        cursor.execute("SELECT cluster_id FROM group_clusters WHERE cluster_name = ? AND is_active = 1", (cluster_name,))
        if cursor.fetchone():
            return False, f"组名 '{cluster_name}' 已存在"
        
        # 创建新组
        cursor.execute(
            "INSERT INTO group_clusters (cluster_name, description, created_by) VALUES (?, ?, ?)",
            (cluster_name, description, created_by)
        )
        cluster_id = cursor.lastrowid
        conn.commit()
        return True, f"成功创建组 '{cluster_name}'（ID: {cluster_id}）"
    except sqlite3.Error as e:
        logger.error(f"创建组失败: {e}")
        return False, f"创建组失败: {e}"
    finally:
        conn.close()

# 删除组
def do_delete_cluster(cluster_name: str) -> tuple[bool, str]:
    """删除组（软删除）
    
    Args:
        cluster_name: 组名
        
    Returns:
        tuple[bool, str]: (是否成功, 消息)
    """
    db_path = data_dir / "group_manager.db"
    conn = sqlite3.connect(str(db_path))
    cursor = conn.cursor()
    
    try:
        # 检查组是否存在
        cursor.execute("SELECT cluster_id FROM group_clusters WHERE cluster_name = ? AND is_active = 1", (cluster_name,))
        cluster_row = cursor.fetchone()
        if not cluster_row:
            return False, f"组 '{cluster_name}' 不存在"
        
        cluster_id = cluster_row[0]
        
        # 软删除组
        cursor.execute("UPDATE group_clusters SET is_active = 0 WHERE cluster_id = ?", (cluster_id,))
        
        # 清理组内群聊的关联
        cursor.execute("UPDATE group_config SET cluster_id = NULL WHERE cluster_id = ?", (cluster_id,))
        
        # 清理用户跟踪记录
        cursor.execute("DELETE FROM cluster_user_tracking WHERE cluster_id = ?", (cluster_id,))
        
        conn.commit()
        return True, f"成功删除组 '{cluster_name}'"
    except sqlite3.Error as e:
        logger.error(f"删除组失败: {e}")
        return False, f"删除组失败: {e}"
    finally:
        conn.close()

# 群聊加入组
def do_join_cluster(group_id: int, cluster_name: str) -> tuple[bool, str]:
    """群聊加入组
    
    Args:
        group_id: 群号
        cluster_name: 组名
        
    Returns:
        tuple[bool, str]: (是否成功, 消息)
    """
    db_path = data_dir / "group_manager.db"
    conn = sqlite3.connect(str(db_path))
    cursor = conn.cursor()
    
    try:
        # 检查组是否存在
        cursor.execute("SELECT cluster_id FROM group_clusters WHERE cluster_name = ? AND is_active = 1", (cluster_name,))
        cluster_row = cursor.fetchone()
        if not cluster_row:
            return False, f"组 '{cluster_name}' 不存在"
        
        cluster_id = cluster_row[0]
        
        # 检查群聊是否已在其他组中
        cursor.execute("SELECT cluster_id FROM group_config WHERE group_id = ? AND cluster_id IS NOT NULL", (group_id,))
        current_cluster = cursor.fetchone()
        if current_cluster:
            cursor.execute("SELECT cluster_name FROM group_clusters WHERE cluster_id = ?", (current_cluster[0],))
            current_cluster_name = cursor.fetchone()[0]
            return False, f"当前群聊已在组 '{current_cluster_name}' 中，请先退出当前组"
        
        # 加入组
        cursor.execute("UPDATE group_config SET cluster_id = ? WHERE group_id = ?", (cluster_id, group_id))
        conn.commit()
        return True, f"成功加入组 '{cluster_name}'"
    except sqlite3.Error as e:
        logger.error(f"加入组失败: {e}")
        return False, f"加入组失败: {e}"
    finally:
        conn.close()

# 群聊退出组
def do_quit_cluster(group_id: int) -> tuple[bool, str]:
    """群聊退出组
    
    Args:
        group_id: 群号
        
    Returns:
        tuple[bool, str]: (是否成功, 消息)
    """
    db_path = data_dir / "group_manager.db"
    conn = sqlite3.connect(str(db_path))
    cursor = conn.cursor()
    
    try:
        # 检查群聊是否在组中
        cursor.execute("SELECT cluster_id FROM group_config WHERE group_id = ? AND cluster_id IS NOT NULL", (group_id,))
        cluster_row = cursor.fetchone()
        if not cluster_row:
            return False, "当前群聊不在任何组中"
        
        cluster_id = cluster_row[0]
        
        # 获取组名
        cursor.execute("SELECT cluster_name FROM group_clusters WHERE cluster_id = ?", (cluster_id,))
        cluster_name = cursor.fetchone()[0]
        
        # 退出组
        cursor.execute("UPDATE group_config SET cluster_id = NULL WHERE group_id = ?", (group_id,))
        
        # 清理该群的用户跟踪记录
        cursor.execute("DELETE FROM cluster_user_tracking WHERE cluster_id = ? AND group_id = ?", (cluster_id, group_id))
        
        conn.commit()
        return True, f"成功退出组 '{cluster_name}'"
    except sqlite3.Error as e:
        logger.error(f"退出组失败: {e}")
        return False, f"退出组失败: {e}"
    finally:
        conn.close()

# 检查用户是否在组内
async def check_user_in_cluster(bot, user_id: int, cluster_id: int) -> tuple[bool, int]:
    """检查用户是否在指定组内
    
    Args:
        bot: Bot实例（用于实时查询群成员）
        user_id: 用户QQ号
        cluster_id: 组ID
        
    Returns:
        tuple[bool, int]: (是否在组内, 所在群号，如果不在组内则为0)
    """
    db_path = data_dir / "group_manager.db"
    conn = sqlite3.connect(str(db_path))
    cursor = conn.cursor()
    
    try:
        # 首先查询跟踪表（快速路径）
        cursor.execute(
            "SELECT group_id FROM cluster_user_tracking WHERE cluster_id = ? AND user_id = ? AND status = 'active'",
            (cluster_id, user_id)
        )
        result = cursor.fetchone()
        if result:
            return True, result[0]
        
        # 如果跟踪表中没有记录，实时查询组内所有群聊的成员列表
        logger.info(f"跟踪表中未找到用户 {user_id} 在组 {cluster_id} 的记录，开始实时检查")
        
        # 获取组内所有群聊
        cursor.execute("SELECT group_id FROM group_config WHERE cluster_id = ?", (cluster_id,))
        group_results = cursor.fetchall()
        
        if not group_results:
            return False, 0
        
        # 逐个检查群聊成员
        for group_row in group_results:
            group_id = group_row[0]
            try:
                # 获取群成员列表
                member_list = await bot.get_group_member_list(group_id=group_id)
                
                # 检查用户是否在这个群中
                for member in member_list:
                    if member["user_id"] == user_id:
                        logger.warning(f"发现数据不一致：用户 {user_id} 实际在群 {group_id}（组 {cluster_id}）中，但跟踪表中无记录，正在修复")
                        
                        # 自动修复跟踪表数据
                        cursor.execute(
                            "INSERT OR REPLACE INTO cluster_user_tracking (cluster_id, user_id, group_id, status) VALUES (?, ?, ?, ?)",
                            (cluster_id, user_id, group_id, 'active')
                        )
                        conn.commit()
                        
                        logger.info(f"已修复用户 {user_id} 在组 {cluster_id} 群 {group_id} 的跟踪记录")
                        return True, group_id
                        
            except Exception as e:
                logger.warning(f"检查群 {group_id} 成员列表时失败: {e}")
                continue
        
        # 用户确实不在组内任何群聊中
        return False, 0
        
    except sqlite3.Error as e:
        logger.error(f"检查用户组内状态失败: {e}")
        return False, 0
    finally:
        conn.close()

# 更新用户组内状态
def update_user_cluster_status(user_id: int, cluster_id: int, group_id: int, status: str = 'active') -> bool:
    """更新用户在组内的状态
    
    Args:
        user_id: 用户QQ号
        cluster_id: 组ID
        group_id: 群号
        status: 状态 ('active', 'left')
        
    Returns:
        bool: 是否成功
    """
    db_path = data_dir / "group_manager.db"
    conn = sqlite3.connect(str(db_path))
    cursor = conn.cursor()
    
    try:
        if status == 'active':
            # 插入新记录，如果已存在则忽略（避免覆盖现有记录）
            cursor.execute(
                "INSERT OR IGNORE INTO cluster_user_tracking (cluster_id, user_id, group_id, status) VALUES (?, ?, ?, ?)",
                (cluster_id, user_id, group_id, status)
            )
        else:
            # 更新状态或删除记录
            cursor.execute(
                "DELETE FROM cluster_user_tracking WHERE cluster_id = ? AND user_id = ?",
                (cluster_id, user_id)
            )
        
        conn.commit()
        return True
    except sqlite3.Error as e:
        logger.error(f"更新用户组内状态失败: {e}")
        return False
    finally:
        conn.close()

# 初始化现有群成员到组跟踪表
async def initialize_existing_users(bot, group_id: int, cluster_id: int) -> tuple[bool, str]:
    """初始化现有群成员到组跟踪表
    
    Args:
        bot: Bot实例
        group_id: 群号
        cluster_id: 组ID
        
    Returns:
        tuple[bool, str]: (是否成功, 消息)
    """
    try:
        # 获取群成员列表
        member_list = await bot.get_group_member_list(group_id=group_id)
        
        if not member_list:
            return True, "群成员列表为空，无需初始化"
        
        db_path = data_dir / "group_manager.db"
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()
        
        # 过滤掉机器人自己
        bot_info = await bot.get_login_info()
        bot_user_id = bot_info["user_id"]
        
        success_count = 0
        for member in member_list:
            user_id = member["user_id"]
            
            # 跳过机器人自己
            if user_id == bot_user_id:
                continue
                
            try:
                # 检查用户是否已在同组的其他群中
                cursor.execute(
                    "SELECT group_id FROM cluster_user_tracking WHERE cluster_id = ? AND user_id = ? AND status = 'active'",
                    (cluster_id, user_id)
                )
                existing_record = cursor.fetchone()
                
                if existing_record:
                    # 用户已在同组的其他群中，跳过
                    logger.info(f"用户 {user_id} 已在同组的群 {existing_record[0]} 中，跳过初始化")
                    continue
                
                # 添加用户到跟踪表
                cursor.execute(
                    "INSERT OR REPLACE INTO cluster_user_tracking (cluster_id, user_id, group_id, status) VALUES (?, ?, ?, ?)",
                    (cluster_id, user_id, group_id, 'active')
                )
                success_count += 1
                
            except sqlite3.Error as e:
                logger.error(f"初始化用户 {user_id} 失败: {e}")
                continue
        
        conn.commit()
        conn.close()
        
        logger.info(f"成功初始化群 {group_id} 的 {success_count} 个用户到组 {cluster_id}")
        return True, f"成功初始化 {success_count} 个现有群成员"
        
    except Exception as e:
        logger.error(f"初始化现有用户失败: {e}")
        return False, f"初始化现有用户失败: {e}"

# 实时计算组群人数
async def calculate_cluster_member_count(bot, cluster_id: int) -> int:
    """实时计算组内所有群的去重用户数
    
    Args:
        bot: Bot实例
        cluster_id: 组ID
        
    Returns:
        int: 去重后的用户总数
    """
    try:
        # 获取组内所有群聊
        db_path = data_dir / "group_manager.db"
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()
        
        cursor.execute("SELECT group_id FROM group_config WHERE cluster_id = ?", (cluster_id,))
        group_results = cursor.fetchall()
        conn.close()
        
        if not group_results:
            return 0
        
        # 获取机器人自己的ID
        try:
            bot_info = await bot.get_login_info()
            bot_user_id = bot_info["user_id"]
        except Exception as e:
            logger.warning(f"获取机器人信息失败: {e}")
            bot_user_id = None
        
        # 收集所有群的用户ID
        all_user_ids = set()
        
        for group_row in group_results:
            group_id = group_row[0]
            try:
                # 获取群成员列表
                member_list = await bot.get_group_member_list(group_id=group_id)
                
                for member in member_list:
                    user_id = member["user_id"]
                    # 排除机器人自己
                    if bot_user_id and user_id == bot_user_id:
                        continue
                    all_user_ids.add(user_id)
                    
            except Exception as e:
                logger.warning(f"获取群 {group_id} 成员列表失败: {e}")
                continue
        
        return len(all_user_ids)
        
    except Exception as e:
        logger.error(f"计算组群人数失败: {e}")
        return -1  # 返回-1表示计算失败

# 获取组信息
async def get_cluster_info(bot=None, cluster_name: str = None, cluster_id: int = None) -> dict:
    """获取组信息
    
    Args:
        bot: Bot实例（可选，用于实时计算组群人数）
        cluster_name: 组名（可选）
        cluster_id: 组ID（可选）
        
    Returns:
        dict: 组信息
    """
    db_path = data_dir / "group_manager.db"
    conn = sqlite3.connect(str(db_path))
    cursor = conn.cursor()
    
    try:
        if cluster_name:
            cursor.execute(
                "SELECT cluster_id, cluster_name, description, created_time, created_by FROM group_clusters WHERE cluster_name = ? AND is_active = 1",
                (cluster_name,)
            )
        elif cluster_id:
            cursor.execute(
                "SELECT cluster_id, cluster_name, description, created_time, created_by FROM group_clusters WHERE cluster_id = ? AND is_active = 1",
                (cluster_id,)
            )
        else:
            return {}
        
        result = cursor.fetchone()
        if not result:
            return {}
        
        cluster_info = {
            "cluster_id": result[0],
            "cluster_name": result[1],
            "description": result[2],
            "created_time": result[3],
            "created_by": result[4]
        }
        
        # 获取组内群聊列表
        cursor.execute(
            "SELECT group_id FROM group_config WHERE cluster_id = ?",
            (cluster_info["cluster_id"],)
        )
        groups = [row[0] for row in cursor.fetchall()]
        cluster_info["groups"] = groups
        
        # 获取组群人数
        if bot:
            # 使用实时计算
            member_count = await calculate_cluster_member_count(bot, cluster_info["cluster_id"])
            cluster_info["member_count"] = member_count if member_count >= 0 else "计算失败"
        else:
            # 回退到基于跟踪表的统计（兼容性）
            cursor.execute(
                "SELECT COUNT(DISTINCT user_id) FROM cluster_user_tracking WHERE cluster_id = ? AND status = 'active'",
                (cluster_info["cluster_id"],)
            )
            user_count = cursor.fetchone()[0]
            cluster_info["member_count"] = user_count
        
        return cluster_info
    except sqlite3.Error as e:
        logger.error(f"获取组信息失败: {e}")
        return {}
    finally:
        conn.close()

# 获取所有组列表
async def get_all_clusters(bot=None) -> list:
    """获取所有活跃的组
    
    Args:
        bot: Bot实例（可选，用于实时计算组群人数）
        
    Returns:
        list: 组信息列表
    """
    db_path = data_dir / "group_manager.db"
    conn = sqlite3.connect(str(db_path))
    cursor = conn.cursor()
    
    try:
        if bot:
            # 使用实时计算的查询
            cursor.execute("""
                SELECT 
                    gc.cluster_id,
                    gc.cluster_name,
                    gc.description,
                    gc.created_time,
                    gc.created_by,
                    COUNT(DISTINCT g.group_id) as group_count
                FROM group_clusters gc
                LEFT JOIN group_config g ON gc.cluster_id = g.cluster_id
                WHERE gc.is_active = 1
                GROUP BY gc.cluster_id, gc.cluster_name, gc.description, gc.created_time, gc.created_by
                ORDER BY gc.created_time DESC
            """)
            
            results = cursor.fetchall()
            clusters = []
            for row in results:
                cluster_id = row[0]
                # 实时计算组群人数
                member_count = await calculate_cluster_member_count(bot, cluster_id)
                
                clusters.append({
                    'cluster_id': cluster_id,
                    'cluster_name': row[1],
                    'description': row[2] or '',
                    'created_time': row[3],
                    'created_by': row[4],
                    'group_count': row[5],
                    'member_count': member_count if member_count >= 0 else "计算失败"
                })
        else:
            # 回退到基于跟踪表的统计（兼容性）
            cursor.execute("""
                SELECT 
                    gc.cluster_id,
                    gc.cluster_name,
                    gc.description,
                    gc.created_time,
                    gc.created_by,
                    COUNT(DISTINCT g.group_id) as group_count,
                    COUNT(DISTINCT cut.user_id) as user_count
                FROM group_clusters gc
                LEFT JOIN group_config g ON gc.cluster_id = g.cluster_id
                LEFT JOIN cluster_user_tracking cut ON gc.cluster_id = cut.cluster_id AND cut.status = 'active'
                WHERE gc.is_active = 1
                GROUP BY gc.cluster_id, gc.cluster_name, gc.description, gc.created_time, gc.created_by
                ORDER BY gc.created_time DESC
            """)
            
            results = cursor.fetchall()
            clusters = []
            for row in results:
                clusters.append({
                    'cluster_id': row[0],
                    'cluster_name': row[1],
                    'description': row[2] or '',
                    'created_time': row[3],
                    'created_by': row[4],
                    'group_count': row[5],
                    'member_count': row[6]
                })
        
        return clusters
    except sqlite3.Error as e:
        logger.error(f"获取组列表失败: {e}")
        return []
    finally:
        conn.close()

# 处理多重申请的函数
async def handle_multiple_applications(bot, user_id: int, target_group_id: int) -> tuple[bool, str, list]:
    """处理用户同时申请多个群的情况
    
    Args:
        bot: Bot实例
        user_id: 用户QQ号
        target_group_id: 目标群号
        
    Returns:
        tuple[bool, str, list]: (是否允许, 消息, 冲突的群列表)
    """
    db_path = data_dir / "group_manager.db"
    conn = sqlite3.connect(str(db_path))
    cursor = conn.cursor()
    
    try:
        # 检查目标群是否在组中
        cursor.execute("SELECT cluster_id FROM group_config WHERE group_id = ?", (target_group_id,))
        target_cluster_result = cursor.fetchone()
        if not target_cluster_result or not target_cluster_result[0]:
            # 目标群不在任何组中，允许加入
            return True, "", []
        
        target_cluster_id = target_cluster_result[0]
        
        # 检查用户是否已在同组的其他群中
        is_in_cluster, existing_group_id = await check_user_in_cluster(bot, user_id, target_cluster_id)
        if is_in_cluster:
            return False, f"用户已在同组的群聊 {existing_group_id} 中", [existing_group_id]
        
        # 检查用户是否有其他待处理的同组申请
        # 这里我们可以通过检查最近的申请记录来判断
        # 由于QQ机器人API限制，我们无法直接获取待处理的申请列表
        # 所以我们采用时间窗口的方式来检测可能的多重申请
        
        # 检查最近5分钟内是否有同组的其他群的申请记录
        five_minutes_ago = datetime.datetime.now() - datetime.timedelta(minutes=5)
        
        cursor.execute("""
            SELECT DISTINCT rh.group_id, gc.cluster_name
            FROM reject_history rh
            JOIN group_config g ON rh.group_id = g.group_id
            JOIN group_clusters gc ON g.cluster_id = gc.cluster_id
            WHERE rh.user_id = ? 
            AND g.cluster_id = ?
            AND rh.group_id != ?
            AND rh.reject_time > ?
            AND rh.reason LIKE '%用户已在同组%'
        """, (user_id, target_cluster_id, target_group_id, five_minutes_ago))
        
        recent_conflicts = cursor.fetchall()
        if recent_conflicts:
            conflict_groups = [str(row[0]) for row in recent_conflicts]
            cluster_name = recent_conflicts[0][1]
            return False, f"检测到您最近在同组（{cluster_name}）的其他群聊中有申请记录，请等待处理完成", conflict_groups
        
        # 如果没有冲突，允许申请
        return True, "", []
        
    except sqlite3.Error as e:
        logger.error(f"处理多重申请检查失败: {e}")
        return True, "", []  # 出错时默认允许，避免误拦截
    finally:
        conn.close()

# ===== 组管理相关辅助函数结束 =====

# ===== 多类型共享管理函数 =====

def migrate_legacy_shares(group_id: int) -> bool:
    """迁移旧的共享配置到新的多类型共享表"""
    db_path = data_dir / "group_manager.db"
    conn = sqlite3.connect(str(db_path))
    cursor = conn.cursor()

    try:
        # 检查是否有旧的共享配置
        cursor.execute(
            "SELECT share_from, share_type FROM group_config WHERE group_id = ? AND share_from > 0 AND share_type IS NOT NULL",
            (group_id,)
        )
        legacy_share = cursor.fetchone()

        if legacy_share:
            share_from, share_type = legacy_share

            # 检查新表中是否已存在该配置
            cursor.execute(
                "SELECT 1 FROM group_shares WHERE group_id = ? AND share_type = ?",
                (group_id, share_type)
            )

            if not cursor.fetchone():
                # 迁移到新表
                cursor.execute(
                    "INSERT INTO group_shares (group_id, share_type, share_from) VALUES (?, ?, ?)",
                    (group_id, share_type, share_from)
                )
                logger.info(f"已迁移群 {group_id} 的 {share_type} 共享配置到新表")

        conn.commit()
        return True
    except sqlite3.Error as e:
        logger.error(f"迁移旧共享配置失败: {e}")
        return False
    finally:
        conn.close()

def get_group_shares(group_id: int) -> dict:
    """获取群组的所有共享配置（包含旧配置的自动迁移）"""
    # 先尝试迁移旧配置
    migrate_legacy_shares(group_id)

    db_path = data_dir / "group_manager.db"
    conn = sqlite3.connect(str(db_path))
    cursor = conn.cursor()

    try:
        # 从新表获取共享配置
        cursor.execute(
            "SELECT share_type, share_from FROM group_shares WHERE group_id = ?",
            (group_id,)
        )
        shares = {}
        for share_type, share_from in cursor.fetchall():
            shares[share_type] = share_from

        # 如果新表为空，检查旧表（兼容性处理）
        if not shares:
            cursor.execute(
                "SELECT share_from, share_type FROM group_config WHERE group_id = ? AND share_from > 0 AND share_type IS NOT NULL",
                (group_id,)
            )
            legacy_share = cursor.fetchone()
            if legacy_share:
                share_from, share_type = legacy_share
                shares[share_type] = share_from

        return shares
    except sqlite3.Error as e:
        logger.error(f"获取群组共享配置失败: {e}")
        return {}
    finally:
        conn.close()

def set_group_share(group_id: int, share_type: str, share_from: int) -> bool:
    """设置群组共享配置（同时更新新旧表以保持兼容性）"""
    db_path = data_dir / "group_manager.db"
    conn = sqlite3.connect(str(db_path))
    cursor = conn.cursor()

    try:
        # 更新新表
        cursor.execute(
            "INSERT OR REPLACE INTO group_shares (group_id, share_type, share_from) VALUES (?, ?, ?)",
            (group_id, share_type, share_from)
        )

        # 为了兼容性，同时更新旧表（如果是配置或黑名单）
        if share_type in ["配置", "黑名单"]:
            cursor.execute(
                "UPDATE group_config SET share_from = ?, share_type = ? WHERE group_id = ?",
                (share_from, share_type, group_id)
            )

        conn.commit()
        return True
    except sqlite3.Error as e:
        logger.error(f"设置群组共享配置失败: {e}")
        return False
    finally:
        conn.close()

def remove_group_share(group_id: int, share_type: str) -> bool:
    """移除群组共享配置（同时更新新旧表以保持兼容性）"""
    db_path = data_dir / "group_manager.db"
    conn = sqlite3.connect(str(db_path))
    cursor = conn.cursor()

    try:
        # 从新表删除
        cursor.execute(
            "DELETE FROM group_shares WHERE group_id = ? AND share_type = ?",
            (group_id, share_type)
        )
        success = cursor.rowcount > 0

        # 为了兼容性，同时更新旧表（如果是配置或黑名单）
        if share_type in ["配置", "黑名单"]:
            cursor.execute(
                "UPDATE group_config SET share_from = 0, share_type = NULL WHERE group_id = ? AND share_type = ?",
                (group_id, share_type)
            )

        conn.commit()
        return success
    except sqlite3.Error as e:
        logger.error(f"移除群组共享配置失败: {e}")
        return False
    finally:
        conn.close()

def get_effective_group_id_for_share_type(group_id: int, share_type: str) -> int:
    """获取指定共享类型的有效群ID"""
    shares = get_group_shares(group_id)
    return shares.get(share_type, group_id)

# ===== 多类型共享管理函数结束 =====

# ===== 发言管理相关辅助函数 =====

def add_banned_word(group_id: int, word: str, action: str = 'recall',
                   punishment_type: str = 'mute', punishment_duration: int = 60,
                   violation_threshold: int = 3, created_by: int = None) -> bool:
    """添加违规词"""
    # 获取有效的群ID（考虑共享）
    effective_group_id = get_effective_group_id_for_share_type(group_id, '违规词')

    db_path = data_dir / "group_manager.db"
    conn = sqlite3.connect(str(db_path))
    cursor = conn.cursor()

    try:
        cursor.execute(
            "INSERT OR REPLACE INTO banned_words (group_id, word, action, punishment_type, punishment_duration, violation_threshold, created_by) VALUES (?, ?, ?, ?, ?, ?, ?)",
            (effective_group_id, word.lower(), action, punishment_type, punishment_duration, violation_threshold, created_by)
        )
        conn.commit()
        return True
    except sqlite3.Error as e:
        logger.error(f"添加违规词失败: {e}")
        return False
    finally:
        conn.close()

def remove_banned_word(group_id: int, word: str) -> bool:
    """移除违规词"""
    # 获取有效的群ID（考虑共享）
    effective_group_id = get_effective_group_id_for_share_type(group_id, '违规词')

    db_path = data_dir / "group_manager.db"
    conn = sqlite3.connect(str(db_path))
    cursor = conn.cursor()

    try:
        cursor.execute(
            "DELETE FROM banned_words WHERE group_id = ? AND word = ?",
            (effective_group_id, word.lower())
        )
        success = cursor.rowcount > 0
        conn.commit()
        return success
    except sqlite3.Error as e:
        logger.error(f"移除违规词失败: {e}")
        return False
    finally:
        conn.close()

def get_banned_words(group_id: int) -> list:
    """获取群组违规词列表"""
    # 获取有效的群ID（考虑共享）
    effective_group_id = get_effective_group_id_for_share_type(group_id, '违规词')

    db_path = data_dir / "group_manager.db"
    conn = sqlite3.connect(str(db_path))
    cursor = conn.cursor()

    try:
        cursor.execute(
            "SELECT word, action, punishment_type, punishment_duration, violation_threshold FROM banned_words WHERE group_id = ? ORDER BY word",
            (effective_group_id,)
        )
        return cursor.fetchall()
    except sqlite3.Error as e:
        logger.error(f"获取违规词列表失败: {e}")
        return []
    finally:
        conn.close()

def check_message_for_violations(group_id: int, message: str) -> tuple:
    """检查消息是否包含违规词"""
    banned_words = get_banned_words(group_id)
    message_lower = message.lower()

    for word_data in banned_words:
        word = word_data[0]
        action = word_data[1]
        punishment_type = word_data[2] if len(word_data) > 2 else 'mute'
        punishment_duration = word_data[3] if len(word_data) > 3 else 60
        violation_threshold = word_data[4] if len(word_data) > 4 else 3

        if word in message_lower:
            return True, word, action, punishment_type, punishment_duration, violation_threshold

    return False, None, None, None, None, None

def record_violation(group_id: int, user_id: int, message_id: int, violation_word: str,
                    original_message: str, action_taken: str) -> None:
    """记录违规行为"""
    db_path = data_dir / "group_manager.db"
    conn = sqlite3.connect(str(db_path))
    cursor = conn.cursor()

    try:
        # 记录违规详情
        cursor.execute(
            "INSERT INTO violation_records (group_id, user_id, message_id, violation_word, original_message, action_taken) VALUES (?, ?, ?, ?, ?, ?)",
            (group_id, user_id, message_id, violation_word, original_message, action_taken)
        )

        # 更新违规计数
        cursor.execute(
            "INSERT OR REPLACE INTO violation_counts (group_id, user_id, violation_count, last_violation_time) VALUES (?, ?, COALESCE((SELECT violation_count FROM violation_counts WHERE group_id = ? AND user_id = ?), 0) + 1, CURRENT_TIMESTAMP)",
            (group_id, user_id, group_id, user_id)
        )

        conn.commit()
    except sqlite3.Error as e:
        logger.error(f"记录违规行为失败: {e}")
    finally:
        conn.close()

def get_user_violation_count(group_id: int, user_id: int) -> int:
    """获取用户违规次数"""
    db_path = data_dir / "group_manager.db"
    conn = sqlite3.connect(str(db_path))
    cursor = conn.cursor()

    try:
        cursor.execute(
            "SELECT violation_count FROM violation_counts WHERE group_id = ? AND user_id = ?",
            (group_id, user_id)
        )
        result = cursor.fetchone()
        return result[0] if result else 0
    except sqlite3.Error as e:
        logger.error(f"获取用户违规次数失败: {e}")
        return 0
    finally:
        conn.close()

def reset_user_violations(group_id: int, user_id: int) -> bool:
    """重置用户违规次数"""
    db_path = data_dir / "group_manager.db"
    conn = sqlite3.connect(str(db_path))
    cursor = conn.cursor()

    try:
        cursor.execute(
            "DELETE FROM violation_counts WHERE group_id = ? AND user_id = ?",
            (group_id, user_id)
        )
        conn.commit()
        return True
    except sqlite3.Error as e:
        logger.error(f"重置用户违规次数失败: {e}")
        return False
    finally:
        conn.close()

def get_speech_monitor_config(group_id: int) -> dict:
    """获取发言监控配置（考虑共享机制）"""
    # 检查是否共享配置
    shares = get_group_shares(group_id)
    effective_group_id = shares.get('配置', group_id)

    # 从有效群ID获取配置
    config = load_config(effective_group_id)
    return {
        'enabled': config.get('speech_monitor_enabled', 0),
        'threshold': config.get('violation_threshold', 3),
        'punishment_type': config.get('punishment_type', 'mute'),
        'punishment_duration': config.get('punishment_duration', 60)
    }

def update_speech_monitor_config(group_id: int, **kwargs) -> bool:
    """更新发言监控配置（考虑共享机制）"""
    # 检查是否共享配置，如果共享则更新源群的配置
    shares = get_group_shares(group_id)
    effective_group_id = shares.get('配置', group_id)

    db_path = data_dir / "group_manager.db"
    conn = sqlite3.connect(str(db_path))
    cursor = conn.cursor()

    try:
        for key, value in kwargs.items():
            if key in ['speech_monitor_enabled', 'violation_threshold', 'punishment_type', 'punishment_duration']:
                cursor.execute(
                    f"UPDATE group_config SET {key} = ? WHERE group_id = ?",
                    (value, effective_group_id)  # 使用有效群ID
                )
        conn.commit()
        logger.info(f"已更新群 {effective_group_id} 的发言监控配置（请求来自群 {group_id}）")
        return True
    except sqlite3.Error as e:
        logger.error(f"更新发言监控配置失败: {e}")
        return False
    finally:
        conn.close()

# ===== 发言管理相关辅助函数结束 =====

# 格式化消息
async def format_message(bot: Bot, msg: str, user_id: int, group_id: int) -> Message:
    try:
        logger.info(f"开始格式化消息，原始消息：{msg}")
        
        # 获取用户信息
        try:
            user_info = await bot.get_stranger_info(user_id=user_id)
            logger.info(f"获取用户信息成功：{user_info}")
        except Exception as e:
            logger.warning(f"获取用户信息失败: {e}")
            user_info = {"nickname": str(user_id)}
        
        # 获取群信息
        try:
            group_info = await bot.get_group_info(group_id=group_id)
            member_list = await bot.get_group_member_list(group_id=group_id)
            group_info["member_count"] = len(member_list)
            logger.info(f"获取群信息成功：{group_info}")
        except Exception as e:
            logger.warning(f"获取群信息失败: {e}")
            group_info = {"group_name": str(group_id), "member_count": 0}
        
        # 处理变量
        logger.info("开始替换变量")
        msg = msg.replace("{at}", f"[CQ:at,qq={user_id}]")
        msg = msg.replace("{nickname}", user_info.get("nickname", str(user_id)))
        msg = msg.replace("{user_id}", str(user_id))
        msg = msg.replace("{group_name}", group_info.get("group_name", str(group_id)))
        msg = msg.replace("{member_count}", str(group_info.get("member_count", 0)))
        logger.info(f"变量替换后的消息：{msg}")
        
        # 处理头像，如果包含头像变量，尝试发送，失败则重试
        if "{avatar}" in msg:
            logger.info("开始处理头像")
            max_retries = 3  # 最大重试次数
            retry_count = 0
            avatar_sizes = [100, 140, 640]  # 尝试不同的尺寸
            avatar_urls = [
                lambda size: f"https://q1.qlogo.cn/g?b=qq&nk={user_id}&s={size}",
                lambda size: f"https://q2.qlogo.cn/headimg_dl?dst_uin={user_id}&spec={size}",
                lambda size: f"https://q.qlogo.cn/g?b=qq&nk={user_id}&s={size}"
            ]
            
            while retry_count < max_retries:
                try:
                    # 构建头像消息 - 使用直接的CQ码格式
                    size = avatar_sizes[min(retry_count, len(avatar_sizes) - 1)]
                    url_func = avatar_urls[min(retry_count, len(avatar_urls) - 1)]
                    avatar_url = url_func(size)
                    logger.info(f"尝试使用头像URL：{avatar_url}")
                    
                    # 添加更多参数来防止URL转换
                    avatar_msg = (
                        f"[CQ:image,file={avatar_url},"
                        f"cache=0,timeout=20,c=3,"
                        f"no_convert=1,no_cache=1,headers={{\"User-Agent\":\"Mozilla/5.0\"}},"
                        f"subType=1,appid=1406]"
                    )
                    
                    # 替换头像标记
                    msg = msg.replace("{avatar}", avatar_msg)
                    logger.info("头像处理成功")
                    break  # 如果成功就跳出循环
                except Exception as e:
                    retry_count += 1
                    logger.warning(f"头像处理第 {retry_count} 次失败: {e}")
                    if retry_count >= max_retries:
                        logger.warning(f"头像处理最终失败，跳过头像")
                        # 如果所有尝试都失败，移除头像标记
                        msg = re.sub(r'{avatar}\n', '', msg)  # 移除单独一行的头像
                        msg = re.sub(r'{avatar}', '', msg)    # 移除头像标记
                        msg = re.sub(r'\[CQ:image.*?\]', '', msg)  # 移除所有图片标记
                    else:
                        # 等待一小段时间后重试
                        await asyncio.sleep(0.5)
        
        # 清理多余的空行
        msg = re.sub(r'\n\s*\n', '\n', msg)
        msg = msg.strip()
        logger.info(f"最终格式化的消息：{msg}")
        
        return Message(msg)
    except Exception as e:
        logger.error(f"格式化消息失败，错误详情：{e}")
        logger.error(f"错误堆栈：", exc_info=True)
        # 返回原始消息而不是默认欢迎语
        return Message(msg)

# 命令处理器
menu = on_command("群管菜单", aliases={"群管帮助"}, priority=5, block=True)
toggle_plugin = on_command("群管理", aliases={"开启群管", "关闭群管"}, permission=ADMIN_PERMISSION, priority=5, block=True)
set_welcome = on_command("设置欢迎语", permission=ADMIN_PERMISSION, priority=5, block=True)
show_welcome = on_command("查看欢迎语", permission=ADMIN_PERMISSION, priority=5, block=True)
set_auto_approve = on_command("自动审核", permission=ADMIN_PERMISSION, priority=5, block=True)
set_qq_level = on_command("设置等级", permission=ADMIN_PERMISSION, priority=5, block=True)
set_no_level_policy = on_command("设置无级审核", aliases={"无级审核"}, permission=ADMIN_PERMISSION, priority=5, block=True)
toggle_private = on_command("私聊欢迎", permission=ADMIN_PERMISSION, priority=5, block=True)
add_blacklist = on_command("添加黑名单", permission=ADMIN_PERMISSION, priority=5, block=True)
remove_blacklist = on_command("移除黑名单", permission=ADMIN_PERMISSION, priority=5, block=True)
show_blacklist = on_command("查看黑名单", permission=ADMIN_PERMISSION, priority=5, block=True)
query_blacklist = on_command("查询黑名单", permission=ADMIN_PERMISSION, priority=5, block=True)
show_config = on_command("群管配置", permission=ADMIN_PERMISSION, priority=5, block=True)
set_leave = on_command("设置退群语", permission=ADMIN_PERMISSION, priority=5, block=True)
show_leave = on_command("查看退群语", permission=ADMIN_PERMISSION, priority=5, block=True)
set_short_welcome = on_command("设置简短欢迎语", permission=ADMIN_PERMISSION, priority=5, block=True)
show_short_welcome = on_command("查看简短欢迎语", permission=ADMIN_PERMISSION, priority=5, block=True)
set_not_friend_tip = on_command("设置非好友提示语", permission=ADMIN_PERMISSION, priority=5, block=True)
show_not_friend_tip = on_command("查看非好友提示语", permission=ADMIN_PERMISSION, priority=5, block=True)
group_tips = on_message(rule=specific_message("群提示"), priority=5, block=True)
copy_config = on_command("复制配置", permission=ADMIN_PERMISSION, priority=5, block=True)
share_config = on_command("共享配置", permission=ADMIN_PERMISSION, priority=5, block=True)
quit_share = on_command("退出共享", permission=ADMIN_PERMISSION, priority=5, block=True)
restore_config = on_command("还原配置", permission=ADMIN_PERMISSION, priority=5, block=True)
restore_blacklist = on_command("还原黑名单", permission=ADMIN_PERMISSION, priority=5, block=True)
restore_confirm = on_message(rule=only_confirm_messages(), priority=5, block=True)
show_reject = on_command("查看拒绝记录", permission=ADMIN_PERMISSION, priority=5, block=True)
query_reject = on_command("查询拒绝记录", permission=ADMIN_PERMISSION, priority=5, block=True)
clear_reject = on_command("清空拒绝记录", permission=ADMIN_PERMISSION, priority=5, block=True)
clear_reject_confirm = on_message(rule=only_confirm_messages(), priority=5, block=True)

# 发言管理命令处理器
toggle_speech_monitor = on_command("发言监控", aliases={"开启发言监控", "关闭发言监控"}, permission=ADMIN_PERMISSION, priority=5, block=True)
add_banned_word_cmd = on_command("添加违规词", permission=ADMIN_PERMISSION, priority=5, block=True)
remove_banned_word_cmd = on_command("移除违规词", permission=ADMIN_PERMISSION, priority=5, block=True)
list_banned_words = on_command("违规词列表", permission=ADMIN_PERMISSION, priority=5, block=True)
set_violation_threshold = on_command("设置违规阈值", permission=ADMIN_PERMISSION, priority=5, block=True)
set_punishment = on_command("设置处罚方式", permission=ADMIN_PERMISSION, priority=5, block=True)
check_user_violations = on_command("查看违规记录", permission=ADMIN_PERMISSION, priority=5, block=True)
reset_violations = on_command("重置违规记录", permission=ADMIN_PERMISSION, priority=5, block=True)
speech_monitor_config = on_command("发言监控配置", permission=ADMIN_PERMISSION, priority=5, block=True)

# 多类型共享命令处理器
share_multi_config = on_command("共享多配置", permission=ADMIN_PERMISSION, priority=5, block=True)
quit_multi_share = on_command("退出多共享", permission=ADMIN_PERMISSION, priority=5, block=True)
show_shares = on_command("查看共享状态", permission=ADMIN_PERMISSION, priority=5, block=True)
migrate_shares = on_command("迁移共享配置", permission=ADMIN_PERMISSION, priority=5, block=True)

# 高级违规词管理命令
add_advanced_banned_word = on_command("添加高级违规词", permission=ADMIN_PERMISSION, priority=5, block=True)
modify_banned_word = on_command("修改违规词", permission=ADMIN_PERMISSION, priority=5, block=True)

# 诊断命令
diagnose_speech_monitor = on_command("诊断发言监控", permission=ADMIN_PERMISSION, priority=5, block=True)

# 用于存储待确认的还原操作
restore_confirmations = {
    "config": {},  # 配置还原确认
    "blacklist": {},  # 黑名单还原确认
    "reject": {}  # 拒绝记录清空确认
}

# 事件处理器
group_request = on_request(priority=5)
group_decrease = on_notice(rule=is_group_decrease_event(), priority=5, block=True)
group_increase = on_notice(rule=is_group_increase_event(), priority=5, block=True)

# 自定义规则：只处理@用户的管理命令
def admin_cmd_rule() -> Rule:
    async def _admin_cmd_rule(event: GroupMessageEvent) -> bool:
        # 检查消息是否包含@
        at_segments = [seg for seg in event.message if seg.type == "at"]
        if not at_segments:
            return False
            
        # 检查是否包含管理命令
        msg = event.raw_message
        return ("禁言" in msg or "ban" in msg.lower() or "mute" in msg.lower() or 
                "解禁" in msg or "unban" in msg.lower() or "unmute" in msg.lower() or
                "踢" in msg or "kick" in msg.lower())
    return Rule(_admin_cmd_rule)

admin_msg = on_message(rule=admin_cmd_rule(), priority=10, block=True)

# 发言监控规则：排除命令消息和管理员消息
def speech_monitor_rule() -> Rule:
    async def _speech_monitor_rule(event: GroupMessageEvent) -> bool:
        # 排除命令消息
        if event.raw_message.lstrip().startswith("#"):
            return False

        # 检查插件是否启用
        if not is_plugin_enabled(event.group_id):
            logger.debug(f"群 {event.group_id} 插件未启用，跳过发言监控")
            return False

        # 检查是否启用发言监控（考虑共享配置）
        config = get_speech_monitor_config(event.group_id)
        if not config['enabled']:
            logger.debug(f"群 {event.group_id} 发言监控未启用，跳过检测")
            return False

        # 排除管理员和超级用户
        if event.sender.role in ["admin", "owner"]:
            logger.debug(f"群 {event.group_id} 用户 {event.user_id} 是管理员，跳过发言监控")
            return False

        logger.debug(f"群 {event.group_id} 用户 {event.user_id} 消息 '{event.raw_message}' 进入发言监控检测")
        return True
    return Rule(_speech_monitor_rule)

speech_monitor = on_message(rule=speech_monitor_rule(), priority=15, block=False)

# 帮助菜单
help_text = """
=====群管理系统=====

【插件开关】
#群管理 开启/关闭 - 开启或关闭群管理功能
※ 默认关闭，需要先开启才能使用其他功能

【入群管理】
① #自动审核 开启/关闭 - 设置是否自动审核
② #设置等级 [等级] - 设置入群最低QQ等级要求（0表示不限制）
③ #设置欢迎语 [欢迎语] - 设置入群欢迎语
④ #查看欢迎语 - 查看当前欢迎语
⑤ #私聊欢迎 开启/关闭 - 设置欢迎语是否私聊发送
⑥ #群管配置 - 查看当前群的所有配置
⑦ #设置简短欢迎语 [欢迎语] - 设置非好友时的简短欢迎语
⑧ #查看简短欢迎语 - 查看当前简短欢迎语
⑨ #设置非好友提示语 [提示语] - 设置非好友发送群提示时的提示语
⑩ #查看非好友提示语 - 查看当前非好友提示语
⑪ #设置无级审核 允许/拒绝 - 设置无法获取QQ等级时是否允许加入

【退群管理】
① #设置退群语 [退群提示] - 设置成员退群时的提示语
② #查看退群语 - 查看当前退群提示语

【配置共享】
① #复制配置 [群号] [配置/黑名单] - 复制指定群的配置或黑名单
② #共享配置 [群号] [配置/黑名单] - 与指定群共享配置或黑名单
③ #退出共享 [配置/黑名单] - 退出当前群指定类型的共享状态
④ #共享多配置 [群号] [类型1,类型2...] - 同时共享多种配置类型
⑤ #退出多共享 [类型1,类型2...] - 退出多种共享类型
⑥ #查看共享状态 - 查看当前群的所有共享配置
⑦ #迁移共享配置 - 将旧的共享配置迁移到新系统（支持多类型共享）
⑧ #还原配置 - 还原所有配置为默认值
⑨ #还原黑名单 - 清空当前群的黑名单
※ 支持的共享类型：配置、黑名单、违规词
※ 如果之前使用过旧的共享功能，建议先执行 #迁移共享配置

【拒绝记录管理】
① #查看拒绝记录 - 查看所有拒绝记录
② #查询拒绝记录 [QQ号] - 查询指定QQ号的拒绝记录
③ #清空拒绝记录 - 清空所有拒绝记录

【群管理命令】
① @某人 禁言 [分钟]
② @某人 解禁
③ @某人 踢

【黑名单管理】
① #添加黑名单 [QQ号] - 添加黑名单（支持换行分隔多个QQ号批量添加）
② #移除黑名单 [QQ号] - 移除黑名单（支持换行分隔多个QQ号批量移除）
③ #查看黑名单 - 查看黑名单列表（包含详细信息）
④ #查看黑名单账号 - 仅查看黑名单账号列表
⑤ #查询黑名单 [QQ号] - 查询指定QQ号的黑名单状态
※ 示例：
#添加黑名单
123456789
987654321
111222333

【发言管理功能】
① #发言监控 开启/关闭 - 开启或关闭发言监控功能
② #添加违规词 [违规词] - 添加需要监控的违规词（支持换行分隔多个）
③ #添加高级违规词 [违规词] [处罚方式] [时长] [阈值] - 添加带独立配置的违规词
④ #修改违规词 [违规词] [处罚方式] [时长] [阈值] - 修改违规词的处罚配置
⑤ #移除违规词 [违规词] - 移除违规词（支持换行分隔多个）
⑥ #违规词列表 - 查看当前群的违规词列表（含独立配置）
⑦ #设置违规阈值 [次数] - 设置全局违规阈值（默认3次）
⑧ #设置处罚方式 [禁言/踢出] [时长] - 设置全局处罚方式和时长
⑨ #查看违规记录 [QQ号(可选)] - 查看违规记录
⑩ #重置违规记录 [QQ号] - 重置指定用户的违规次数
⑪ #发言监控配置 - 查看当前发言监控配置
⑫ #诊断发言监控 - 诊断发言监控功能状态和配置
※ 发言监控说明：
- 每个违规词可独立配置处罚方式、时长和阈值
- 检测到违规词会立即撤回消息并记录违规次数
- 优先使用违规词的独立配置，无配置时使用全局配置
- 管理员和群主不受发言监控限制
- 支持不分大小写的包含检测
- 支持违规词配置共享
- 如果功能不生效，请使用 #诊断发言监控 排查问题

【组管理功能】
① #创建组 [组名] [组描述(可选)] - 创建新的群聊组
② #删除组 [组名] - 删除指定的组
③ #加入组 [组名] - 当前群聊加入指定组
④ #退出组 - 当前群聊退出所在组
⑤ #查看组信息 [组名(可选)] - 查看组信息（不指定组名则查看当前群所在组）
⑥ #查看所有组 - 查看所有组的列表
⑦ #重新初始化组 - 重新初始化当前群所在组的用户状态（修复数据不一致问题）
※ 组功能说明：
- 同一组内的群聊，用户不能重复加入
- 用户在组内任一群聊时，申请加入同组其他群聊会被自动拒绝
- 用户退群时会自动清理组内状态，可重新申请加入同组其他群聊

【可用变量】
① {at} - @新成员
② {nickname} - 新成员昵称
③ {user_id} - 新成员QQ号
④ {avatar} - 新成员头像
⑤ {group_name} - 群名称
⑥ {member_count} - 群成员数量

【温馨提示】
※ 请谨慎使用踢人功能
※ 被踢出的成员会自动加入黑名单
※ 主动退群的成员会自动加入黑名单
※ 黑名单成员无法再次加入群聊
※ QQ等级限制会在自动审核开启时生效
※ 黑名单自动踢出需要开启自动审核
※ 非好友用户将收到简短欢迎语
※ 非好友用户使用群提示命令会收到提示添加好友的消息
"""

@menu.handle()
async def handle_menu(event: GroupMessageEvent):
    await menu.finish(help_text)

@toggle_plugin.handle()
async def handle_toggle_plugin(event: GroupMessageEvent, args: Message = CommandArg()):
    status = args.extract_plain_text().strip()
    if status not in ["开启", "关闭"]:
        await toggle_plugin.finish("请使用 开启/关闭")
    
    db_path = data_dir / "group_manager.db"
    conn = sqlite3.connect(str(db_path))
    cursor = conn.cursor()
    
    enabled = 1 if status == "开启" else 0
    cursor.execute(
        "UPDATE group_config SET plugin_enabled = ? WHERE group_id = ?",
        (enabled, event.group_id)
    )
    conn.commit()
    conn.close()
    
    await toggle_plugin.finish(f"群管理功能已{status}")

@set_welcome.handle()
async def handle_set_welcome(event: GroupMessageEvent, bot: Bot, args: Message = CommandArg()):
    if not is_plugin_enabled(event.group_id):
        await set_welcome.finish("群管理功能未启用，请先使用 #群管理 开启")
        return

    welcome_msg = str(event.message).strip()
    if welcome_msg.startswith("#设置欢迎语"):
        welcome_msg = welcome_msg[6:].strip()
    
    if not welcome_msg:
        await set_welcome.finish("请输入欢迎语！")
        return
    
    db_path = data_dir / "group_manager.db"
    conn = sqlite3.connect(str(db_path))
    cursor = conn.cursor()
    
    cursor.execute(
        "UPDATE group_config SET welcome_msg = ? WHERE group_id = ?",
        (welcome_msg, event.group_id)
    )
    conn.commit()
    conn.close()
    
    preview = await format_message(bot, welcome_msg, event.user_id, event.group_id)
    await set_welcome.finish(f"欢迎语设置成功！\n预览效果：\n{preview}")

@show_welcome.handle()
async def handle_show_welcome(event: GroupMessageEvent, bot: Bot):
    config = load_config(event.group_id)
    preview = await format_message(bot, config['welcome_msg'], event.user_id, event.group_id)
    await show_welcome.finish(f"当前欢迎语：\n{config['welcome_msg']}\n\n预览效果：\n{preview}")

@set_auto_approve.handle()
async def handle_set_auto_approve(event: GroupMessageEvent, args: Message = CommandArg()):
    if not is_plugin_enabled(event.group_id):
        await set_auto_approve.finish("群管理功能未启用，请先使用 #群管理 开启")
        return

    status = args.extract_plain_text().strip()
    if status not in ["开启", "关闭"]:
        await set_auto_approve.finish("请使用 开启/关闭")
    
    db_path = data_dir / "group_manager.db"
    conn = sqlite3.connect(str(db_path))
    cursor = conn.cursor()
    
    cursor.execute(
        "UPDATE group_config SET auto_approve = ? WHERE group_id = ?",
        (1 if status == "开启" else 0, event.group_id)
    )
    conn.commit()
    conn.close()
    
    await set_auto_approve.finish(f"自动审核已{status}")

@set_qq_level.handle()
async def handle_set_qq_level(event: GroupMessageEvent, args: Message = CommandArg()):
    config = load_config(event.group_id)
    if not config["plugin_enabled"]:
        await set_qq_level.finish("群管理功能未启用，请先使用 #群管理 开启")
        return

    if not config["auto_approve"]:
        await set_qq_level.finish("设置等级需要先开启自动审核功能")
        return

    level_text = args.extract_plain_text().strip()
    try:
        level = int(level_text)
        if level < 0:
            await set_qq_level.finish("QQ等级不能小于0")
            return
    except ValueError:
        await set_qq_level.finish("请输入有效的数字")
        return
    
    db_path = data_dir / "group_manager.db"
    conn = sqlite3.connect(str(db_path))
    cursor = conn.cursor()
    
    cursor.execute(
        "UPDATE group_config SET min_qq_level = ? WHERE group_id = ?",
        (level, event.group_id)
    )
    conn.commit()
    conn.close()
    
    if level == 0:
        await set_qq_level.finish("已关闭QQ等级限制")
    else:
        await set_qq_level.finish(f"已设置最低QQ等级要求为：{level}")

@set_no_level_policy.handle()
async def handle_set_no_level_policy(event: GroupMessageEvent, args: Message = CommandArg()):
    config = load_config(event.group_id)
    if not config["plugin_enabled"]:
        await set_no_level_policy.finish("群管理功能未启用，请先使用 #群管理 开启")
        return

    if not config["auto_approve"]:
        await set_no_level_policy.finish("设置无级审核需要先开启自动审核功能")
        return
    
    if config["min_qq_level"] <= 0:
        await set_no_level_policy.finish("当前没有设置QQ等级限制，无需配置此项")
        return

    args_text = args.extract_plain_text().strip()
    if args_text not in ["允许", "拒绝"]:
        await set_no_level_policy.finish("参数错误！请使用：#设置无级审核 [允许/拒绝]")
        return
    
    allow = 1 if args_text == "允许" else 0
    
    db_path = data_dir / "group_manager.db"
    conn = sqlite3.connect(str(db_path))
    cursor = conn.cursor()
    
    cursor.execute(
        "UPDATE group_config SET allow_when_no_level = ? WHERE group_id = ?",
        (allow, event.group_id)
    )
    conn.commit()
    conn.close()
    
    if allow:
        await set_no_level_policy.finish("已设置为：当无法获取QQ等级时【允许】加群")
    else:
        await set_no_level_policy.finish("已设置为：当无法获取QQ等级时【拒绝】加群")

@toggle_private.handle()
async def handle_toggle_private(event: GroupMessageEvent, args: Message = CommandArg()):
    if not is_plugin_enabled(event.group_id):
        await toggle_private.finish("群管理功能未启用，请先使用 #群管理 开启")
        return

    status = args.extract_plain_text().strip()
    if status not in ["开启", "关闭"]:
        await toggle_private.finish("请使用 开启/关闭")
    
    db_path = data_dir / "group_manager.db"
    conn = sqlite3.connect(str(db_path))
    cursor = conn.cursor()
    
    enabled = 1 if status == "开启" else 0
    cursor.execute(
        "UPDATE group_config SET private_chat = ? WHERE group_id = ?",
        (enabled, event.group_id)
    )
    conn.commit()
    conn.close()
    
    await toggle_private.finish(f"私聊欢迎功能已{status}")

@add_blacklist.handle()
async def handle_add_blacklist(event: GroupMessageEvent, args: Message = CommandArg()):
    if not is_plugin_enabled(event.group_id):
        await add_blacklist.finish("群管理功能未启用，请先使用 #群管理 开启")
        return

    user_ids_text = args.extract_plain_text().strip()
    if not user_ids_text: # Allow empty to perhaps show usage or current status in future?
        # For now, require user_ids
        await add_blacklist.finish("请提供要加入黑名单的QQ号（支持换行或空格分隔多个QQ号）。")
        return
    
    # 支持空格和换行符分隔
    user_ids_input = user_ids_text.replace('\n', ' ').split()
    if not user_ids_input:
        await add_blacklist.finish("请提供有效的QQ号。")
        return
    
    db_path = data_dir / "group_manager.db"
    conn = sqlite3.connect(str(db_path))
    cursor = conn.cursor()

    effective_blacklist_gid = get_effective_blacklist_group_id(event.group_id, conn)
    # get_effective_blacklist_group_id might commit if share source was invalid and reset
    # No need for an explicit commit here unless other changes are made before this block

    success_ids = []
    failed_ids_invalid_format = []
    
    for user_id_str in user_ids_input:
        if not user_id_str.isdigit():
            failed_ids_invalid_format.append(user_id_str)
            continue
        
        user_id = int(user_id_str)
        try:
            cursor.execute(
                "INSERT OR REPLACE INTO blacklist (group_id, user_id, reason, add_time) VALUES (?, ?, ?, ?)",
                (effective_blacklist_gid, user_id, "管理员添加", datetime.datetime.now())
            )
            success_ids.append(str(user_id))
        except sqlite3.Error as e:
            logger.error(f"向群 {effective_blacklist_gid} 添加黑名单 {user_id} 失败: {e}")
            # This specific user_id failed, do not add to success_ids
            # Add to a new list for specific DB errors if needed, for now, just log
            pass # Or add to a specific failed_db_operation list
    
    conn.commit() # Commit all successful additions and any prior config changes
    conn.close()
    
    msg_parts = []
    if success_ids:
        msg_parts.append(f"已成功将 {len(success_ids)} 个QQ号 ({', '.join(success_ids)}) 添加到群 {effective_blacklist_gid} 的黑名单。")
    if failed_ids_invalid_format:
        msg_parts.append(f"以下 {len(failed_ids_invalid_format)} 个输入因格式无效未能处理：{', '.join(failed_ids_invalid_format)}。")
    
    if not msg_parts:
        await add_blacklist.finish("没有提供有效的QQ号进行操作。")
    else:
        await add_blacklist.finish("\n".join(msg_parts))

@remove_blacklist.handle()
async def handle_remove_blacklist(event: GroupMessageEvent, args: Message = CommandArg()):
    if not is_plugin_enabled(event.group_id):
        await remove_blacklist.finish("群管理功能未启用，请先使用 #群管理 开启")
        return

    # 支持空格和换行符分隔
    user_ids_input = args.extract_plain_text().strip().replace('\n', ' ').split()
    if not user_ids_input:
        await remove_blacklist.finish("请提供要移除黑名单的QQ号（支持换行或空格分隔多个QQ号）。")
        return
    
    db_path = data_dir / "group_manager.db"
    conn = sqlite3.connect(str(db_path))
    cursor = conn.cursor()
    
    effective_blacklist_gid = get_effective_blacklist_group_id(event.group_id, conn)
    # get_effective_blacklist_group_id might commit if share source was invalid and reset.

    success_ids = []
    failed_ids_invalid_format = []
    failed_ids_not_found = []
    failed_ids_db_error = []
    
    for user_id_str in user_ids_input:
        if not user_id_str.isdigit():
            failed_ids_invalid_format.append(user_id_str)
            continue

        user_id = int(user_id_str)
        try:
            # 先检查是否在目标黑名单中
            cursor.execute(
                "SELECT 1 FROM blacklist WHERE group_id = ? AND user_id = ?",
                (effective_blacklist_gid, user_id)
            )
            if cursor.fetchone() is None:
                failed_ids_not_found.append(str(user_id))
                continue
                
            cursor.execute(
                "DELETE FROM blacklist WHERE group_id = ? AND user_id = ?",
                (effective_blacklist_gid, user_id)
            )
            if cursor.rowcount > 0:
                success_ids.append(str(user_id))
            else:
                # This case should ideally not be reached if the above SELECT found the user,
                # but included for robustness (e.g., race condition or unexpected DB state)
                failed_ids_db_error.append(str(user_id))
        except sqlite3.Error as e:
            logger.error(f"从群 {effective_blacklist_gid} 移除黑名单 {user_id} 失败: {e}")
            failed_ids_db_error.append(str(user_id))
    
    conn.commit() # Commit all successful deletions and any prior config changes
    conn.close()
    
    msg_parts = []
    if success_ids:
        msg_parts.append(f"已成功从群 {effective_blacklist_gid} 的黑名单中移除 {len(success_ids)} 个QQ号 ({', '.join(success_ids)})。")
    if failed_ids_invalid_format:
        msg_parts.append(f"以下 {len(failed_ids_invalid_format)} 个输入因格式无效未能处理：{', '.join(failed_ids_invalid_format)}。")
    if failed_ids_not_found:
        msg_parts.append(f"以下 {len(failed_ids_not_found)} 个QQ号在群 {effective_blacklist_gid} 的黑名单中未找到：{', '.join(failed_ids_not_found)}。")
    if failed_ids_db_error:
        msg_parts.append(f"以下 {len(failed_ids_db_error)} 个QQ号在数据库操作时移除失败：{', '.join(failed_ids_db_error)}。")
    
    if not msg_parts: # Should not happen if user_ids_input was not empty
        await remove_blacklist.finish("没有提供有效的QQ号进行操作或所有提供的QQ号均无效。")
    else:
        await remove_blacklist.finish("\n".join(msg_parts))

@show_blacklist.handle()
async def handle_show_blacklist(event: GroupMessageEvent):
    if not is_plugin_enabled(event.group_id):
        await show_blacklist.finish("群管理功能未启用，请先使用 #群管理 开启")
        return

    db_path = data_dir / "group_manager.db"
    conn = sqlite3.connect(str(db_path))
    cursor = conn.cursor()

    effective_blacklist_gid = get_effective_blacklist_group_id(event.group_id, conn)
    # 如果 get_effective_blacklist_group_id 修改了配置，需要提交
    if event.group_id != effective_blacklist_gid:
        # 这表示原先是共享，但共享源可能失效并被重置了，或者是指向了一个有效的共享源
        # 如果共享源失效被重置，get_effective_blacklist_group_id 内部会 commit
        pass # 如果只是切换到有效共享源，则无需额外 commit
    elif cursor.connection.in_transaction: # 检查是否有未提交的事务（由 get_effective_blacklist_group_id 导致）
         conn.commit() # 确保更改被提交
    
    cursor.execute(
        "SELECT user_id, reason, add_time FROM blacklist WHERE group_id = ? ORDER BY add_time DESC",
        (effective_blacklist_gid,)
    )
    results = cursor.fetchall()
    conn.close()
    
    if not results:
        await show_blacklist.finish("黑名单为空")
        return
    
    msg = ["=====黑名单列表====="]
    msg.append("账号列表：")
    for user_id, _, _ in results:
        msg.append(str(user_id))
    
    msg.append("\n详细信息：")
    for user_id, reason, add_time in results:
        msg.append(f"QQ：{user_id}")
        msg.append(f"原因：{reason}")
        msg.append(f"时间：{add_time}")
        msg.append("---")
    
    await show_blacklist.finish("\n".join(msg))

@show_config.handle()
async def handle_show_config(event: GroupMessageEvent, bot: Bot):
    config = load_config(event.group_id)
    
    # 获取群信息
    try:
        group_info = await bot.get_group_info(group_id=event.group_id)
        group_name = group_info["group_name"]
    except Exception:
        group_name = str(event.group_id)
    
    # 获取黑名单数量
    db_path = data_dir / "group_manager.db"
    conn = sqlite3.connect(str(db_path))
    cursor = conn.cursor()
    
    # 根据是否共享黑名单决定查询哪个群的黑名单
    target_group = config["share_from"] if config["share_type"] == "黑名单" else event.group_id
    cursor.execute("SELECT COUNT(*) FROM blacklist WHERE group_id = ?", (target_group,))
    blacklist_count = cursor.fetchone()[0]
    conn.close()
    
    msg = [f"===== {group_name} 配置信息 ====="]
    
    # 共享状态
    if config["share_from"]:
        msg.append(f"\n【共享状态】")
        msg.append(f"正在与群 {config['share_from']} 共享{config['share_type']}")
    
    # 基本状态
    msg.append("\n【基本状态】")
    msg.append(f"群管功能：{'已启用' if config['plugin_enabled'] else '未启用'}")
    msg.append(f"自动审核：{'已启用' if config['auto_approve'] else '未启用'}")
    msg.append(f"私聊欢迎：{'已启用' if config['private_chat'] else '未启用'}")
    msg.append(f"黑名单人数：{blacklist_count} 人")
    
    # 入群限制
    msg.append("\n【入群限制】")
    if config['min_qq_level'] > 0:
        msg.append(f"QQ等级要求：{config['min_qq_level']} 级")
        msg.append(f"无法获取等级时：{'允许加入' if config['allow_when_no_level'] else '拒绝加入'}")
    else:
        msg.append("QQ等级要求：无限制")
    
    # 欢迎语配置
    msg.append("\n【欢迎语配置】")
    msg.append(f"当前欢迎语：\n{config['welcome_msg']}")
    
    # 简短欢迎语配置
    msg.append("\n【简短欢迎语配置】")
    msg.append(f"当前简短欢迎语：\n{config['short_welcome_msg']}")
    
    # 非好友提示语配置
    msg.append("\n【非好友提示语配置】")
    msg.append(f"当前非好友提示语：\n{config['not_friend_tip']}")
    
    # 退群提示配置
    msg.append("\n【退群提示配置】")
    msg.append(f"当前退群提示：\n{config['leave_msg']}")
    
    await show_config.finish("\n".join(msg))

@set_leave.handle()
async def handle_set_leave(event: GroupMessageEvent, bot: Bot, args: Message = CommandArg()):
    if not is_plugin_enabled(event.group_id):
        await set_leave.finish("群管理功能未启用，请先使用 #群管理 开启")
        return

    leave_msg = str(event.message).strip()
    if leave_msg.startswith("#设置退群语"):
        leave_msg = leave_msg[6:].strip()
    
    if not leave_msg:
        await set_leave.finish("请输入退群提示语！")
        return
    
    db_path = data_dir / "group_manager.db"
    conn = sqlite3.connect(str(db_path))
    cursor = conn.cursor()
    
    cursor.execute(
        "UPDATE group_config SET leave_msg = ? WHERE group_id = ?",
        (leave_msg, event.group_id)
    )
    conn.commit()
    conn.close()
    
    preview = await format_message(bot, leave_msg, event.user_id, event.group_id)
    await set_leave.finish(f"退群提示语设置成功！\n预览效果：\n{preview}")

@show_leave.handle()
async def handle_show_leave(event: GroupMessageEvent, bot: Bot):
    config = load_config(event.group_id)
    preview = await format_message(bot, config['leave_msg'], event.user_id, event.group_id)
    await show_leave.finish(f"当前退群提示语：\n{config['leave_msg']}\n\n预览效果：\n{preview}")

@group_request.handle()
async def handle_group_request(event: GroupRequestEvent, bot: Bot):
    config = load_config(event.group_id) # load_config might be heavy, optimize later if needed
    if not config["plugin_enabled"]:
        return
    
    db_path = data_dir / "group_manager.db"
    conn = sqlite3.connect(str(db_path))
    cursor = conn.cursor()

    # 使用新的辅助函数获取实际查询黑名单的群ID
    effective_blacklist_gid = get_effective_blacklist_group_id(event.group_id, conn)
    
    # 检查是否在黑名单中
    cursor.execute(
        "SELECT 1 FROM blacklist WHERE group_id = ? AND user_id = ?",
        (effective_blacklist_gid, event.user_id)
    )
    is_blacklisted = cursor.fetchone() is not None
    
    reject_reason = None
    if is_blacklisted:
        reject_reason = "在黑名单中"
        await bot.set_group_add_request(
            flag=event.flag,
            sub_type="add",
            approve=False,
            reason="您在黑名单中"
        )
    else:
        # 检查组内重复用户和多重申请
        allowed, conflict_msg, conflict_groups = await handle_multiple_applications(bot, event.user_id, event.group_id)
        if not allowed:
            reject_reason = conflict_msg
            await bot.set_group_add_request(
                flag=event.flag,
                sub_type="add",
                approve=False,
                reason="您已在同组的其他群聊中，无法重复加入"
            )
        
        # 如果没有被组内重复检查拒绝，继续原有的审核逻辑
        if not reject_reason and config["auto_approve"]:
            # 如果设置了QQ等级要求
            if config["min_qq_level"] > 0:
                try:
                    user_info = await bot.get_stranger_info(user_id=event.user_id)
                    qq_level = user_info.get("level", 0)
                    if qq_level < config["min_qq_level"]:
                        reject_reason = f"QQ等级不足（{qq_level} < {config['min_qq_level']}）"
                        await bot.set_group_add_request(
                            flag=event.flag,
                            sub_type="add",
                            approve=False,
                            reason=f"您的QQ等级未达到要求，你认为这是错误请看文档，有解决办法"
                        )
                except Exception as e:
                    logger.error(f"获取用户QQ等级失败：{e}")
                    # 根据配置决定获取QQ等级失败时的行为
                    if config["allow_when_no_level"]:
                        logger.info(f"由于无法获取QQ等级信息且配置为允许，允许用户 {event.user_id} 加入群组")
                    else:
                        reject_reason = "无法获取QQ等级信息"
                        await bot.set_group_add_request(
                            flag=event.flag,
                            sub_type="add",
                            approve=False,
                            reason="无法获取您的QQ等级信息，请稍后再试"
                        )
            
            if not reject_reason:
                await bot.set_group_add_request(
                    flag=event.flag,
                    sub_type="add",
                    approve=True
                )
    
    # 记录拒绝原因
    if reject_reason:
        cursor.execute(
            "INSERT INTO reject_history (group_id, user_id, reason, comment) VALUES (?, ?, ?, ?)",
            (event.group_id, event.user_id, reject_reason, event.comment)
        )
        conn.commit() # Commit here after potential modification by get_effective_blacklist_group_id
    
    conn.close()

@group_decrease.handle()
async def handle_group_decrease(event: GroupDecreaseNoticeEvent, bot: Bot):
    config = load_config(event.group_id)
    if not config["plugin_enabled"]:
        logger.info(f"GROUP_DECREASE: Plugin not enabled for group {event.group_id}. Skipping blacklist add and custom leave message.")
        return

    # event.operator_id == event.user_id 表示主动退群
    # event.operator_id != event.self_id 表示操作者不是机器人自己 (即群主或其他管理员踢人)
    if event.operator_id == event.user_id or event.operator_id != event.self_id:
        db_path = data_dir / "group_manager.db"
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()
        logger.info(f"GROUP_DECREASE: Processing user {event.user_id} decrease from group {event.group_id}. Operator: {event.operator_id}")
        
        effective_blacklist_gid = get_effective_blacklist_group_id(event.group_id, conn)
        logger.info(f"GROUP_DECREASE: Effective GID {effective_blacklist_gid} for group {event.group_id} (user: {event.user_id})")
        
        reason = "主动退群" if event.operator_id == event.user_id else "被管理员踢出"
        try:
            logger.info(f"GROUP_DECREASE: Attempting to add user {event.user_id} to blacklist of GID {effective_blacklist_gid} for group {event.group_id} with reason: {reason}")
            cursor.execute(
                "INSERT OR REPLACE INTO blacklist (group_id, user_id, reason, add_time) VALUES (?, ?, ?, ?)",
                (effective_blacklist_gid, event.user_id, reason, datetime.datetime.now())
            )
            conn.commit() # 提交黑名单添加操作
            logger.info(f"GROUP_DECREASE: Successfully added user {event.user_id} to blacklist of GID {effective_blacklist_gid} and committed.")
        except sqlite3.Error as e:
            logger.error(f"GROUP_DECREASE: Failed to add user {event.user_id} to blacklist for GID {effective_blacklist_gid} (original group {event.group_id}). Error: {e}")
        
        # 清理用户组内状态（如果群聊在组中）
        try:
            cursor.execute("SELECT cluster_id FROM group_config WHERE group_id = ?", (event.group_id,))
            cluster_result = cursor.fetchone()
            if cluster_result and cluster_result[0]:  # 如果当前群在某个组中
                cluster_id = cluster_result[0]
                update_user_cluster_status(event.user_id, cluster_id, event.group_id, 'left')
                logger.info(f"用户 {event.user_id} 退出群 {event.group_id}，已清理组内状态（组ID: {cluster_id}）")
        except Exception as e:
            logger.error(f"清理用户组内状态失败: {e}")
        
        finally:
            conn.close()
            logger.info(f"GROUP_DECREASE: Database connection closed for user {event.user_id}, group {event.group_id}.")
        
        leave_msg_content = config['leave_msg']
        # 尝试格式化并发送退群消息，即使黑名单操作失败也应尝试发送
        try:
            logger.info(f"GROUP_DECREASE: Attempting to format and send leave message for user {event.user_id} in group {event.group_id}.")
            leave_msg = await format_message(bot, leave_msg_content, event.user_id, event.group_id)
            await bot.send_group_msg(
                group_id=event.group_id,
                message=leave_msg
            )
            logger.info(f"GROUP_DECREASE: Successfully sent leave message for user {event.user_id} in group {event.group_id}.")
        except Exception as e:
            logger.error(f"发送退群消息失败 (用户: {event.user_id}, 群: {event.group_id}): {e}")
            # 可选: 发送一个简化的退群消息作为备用
            try:
                await bot.send_group_msg(
                    group_id=event.group_id,
                    message=f"用户 {event.user_id} 已离开群聊。"
                )
            except Exception as e2:
                logger.error(f"发送简化退群消息也失败了: {e2}")

@group_increase.handle()
async def handle_group_increase(event: GroupIncreaseNoticeEvent, bot: Bot):
    if event.user_id == event.self_id:
        return
        
    config = load_config(event.group_id)
    if not config["plugin_enabled"]:
        return
    
    db_path = data_dir / "group_manager.db"
    conn = sqlite3.connect(str(db_path))
    cursor = conn.cursor()

    # 使用新的辅助函数获取实际查询黑名单的群ID
    effective_blacklist_gid = get_effective_blacklist_group_id(event.group_id, conn)
    
    cursor.execute(
        "SELECT reason FROM blacklist WHERE group_id = ? AND user_id = ?",
        (effective_blacklist_gid, event.user_id)
    )
    result = cursor.fetchone()
    # 下面几行关于 commit 的注释和代码已由 get_effective_blacklist_group_id 内部 commit 替代
    conn.close() # 关闭连接，后续Welcome消息等不再需要此连接
    
    if result: # result 是从 (可能是共享的) 黑名单中查询得到的
        reason = result[0]
        await bot.send_group_msg(
            group_id=event.group_id,
            message=f"检测到黑名单用户（{event.user_id}）进群\n"
                    f"加入黑名单原因：{reason}\n"
                    f"给你踢飞出去~"
        )
        await asyncio.sleep(1)
        await bot.set_group_kick(
            group_id=event.group_id,
            user_id=event.user_id,
            reject_add_request=True
        )
        return
    
    # 更新用户组内状态（如果群聊在组中）
    try:
        db_path = data_dir / "group_manager.db"
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()
        
        cursor.execute("SELECT cluster_id FROM group_config WHERE group_id = ?", (event.group_id,))
        cluster_result = cursor.fetchone()
        if cluster_result and cluster_result[0]:  # 如果当前群在某个组中
            cluster_id = cluster_result[0]
            
            # 检查用户是否已在同组的其他群中
            cursor.execute(
                "SELECT group_id FROM cluster_user_tracking WHERE cluster_id = ? AND user_id = ? AND status = 'active'",
                (cluster_id, event.user_id)
            )
            existing_record = cursor.fetchone()
            
            if existing_record:
                # 用户已在同组的其他群中，不更新记录，保持原有状态
                logger.warning(f"用户 {event.user_id} 已在同组的群 {existing_record[0]} 中，但仍成功加入了群 {event.group_id}，这可能表示组功能存在问题")
            else:
                # 用户不在同组的其他群中，添加新记录
                update_user_cluster_status(event.user_id, cluster_id, event.group_id, 'active')
                logger.info(f"用户 {event.user_id} 加入群 {event.group_id}，已更新组内状态（组ID: {cluster_id}）")
        
        conn.close()
    except Exception as e:
        logger.error(f"更新用户组内状态失败: {e}")
    
    try:
        # 获取用户信息
        try:
            user_info = await bot.get_stranger_info(user_id=event.user_id)
            nickname = user_info.get("nickname", str(event.user_id))
        except Exception as e:
            logger.warning(f"获取用户信息失败: {e}")
            nickname = str(event.user_id)
        
        # 获取群信息
        try:
            group_info = await bot.get_group_info(group_id=event.group_id)
            member_list = await bot.get_group_member_list(group_id=event.group_id)
            member_count = len(member_list)
            group_name = group_info.get("group_name", str(event.group_id))
        except Exception as e:
            logger.warning(f"获取群信息失败: {e}")
            member_count = 0
            group_name = str(event.group_id)
        
        if config["private_chat"]:
            try:
                # 检查是否为好友关系
                friend_list = await bot.get_friend_list()
                is_friend = any(friend["user_id"] == event.user_id for friend in friend_list)
                
                if not is_friend:
                    logger.info(f"用户 {event.user_id} 不是机器人好友，发送简短欢迎语")
                    # 移除图片标记后发送简短欢迎语
                    short_msg = config['short_welcome_msg']
                    if '[CQ:image' in short_msg:
                        short_msg = re.sub(r'\[CQ:image.*?\]', '', short_msg)
                        short_msg = re.sub(r'\n\s*\n', '\n', short_msg)
                    await bot.send_group_msg(
                        group_id=event.group_id,
                        message=await format_message(bot, short_msg, event.user_id, event.group_id)
                    )
                else:
                    # 尝试私聊发送
                    welcome_msg = config['welcome_msg']
                    if '[CQ:image' in welcome_msg:
                        welcome_msg = re.sub(r'\[CQ:image.*?\]', '', welcome_msg)
                        welcome_msg = re.sub(r'\n\s*\n', '\n', welcome_msg)
                    try:
                        await bot.send_private_msg(
                            user_id=event.user_id,
                            message=await format_message(bot, welcome_msg, event.user_id, event.group_id)
                        )
                        await bot.send_group_msg(
                            group_id=event.group_id,
                            message=f"已私聊发送欢迎消息给 {MessageSegment.at(event.user_id)}"
                        )
                    except Exception as e:
                        logger.error(f"私聊发送失败: {e}")
                        # 如果私聊失败，发送简短的群欢迎消息
                        short_msg = config['short_welcome_msg']
                        if '[CQ:image' in short_msg:
                            short_msg = re.sub(r'\[CQ:image.*?\]', '', short_msg)
                            short_msg = re.sub(r'\n\s*\n', '\n', short_msg)
                        await bot.send_group_msg(
                            group_id=event.group_id,
                            message=await format_message(bot, short_msg, event.user_id, event.group_id)
                        )
            except Exception as e:
                logger.error(f"处理私聊欢迎消息失败: {e}")
                # 发送最简单的欢迎消息作为后备方案
                await bot.send_group_msg(
                    group_id=event.group_id,
                    message=f"欢迎 {MessageSegment.at(event.user_id)} 加入本群！"
                )
        else:
            # 群聊发送
            welcome_msg = config['welcome_msg']
            if '[CQ:image' in welcome_msg:
                welcome_msg = re.sub(r'\[CQ:image.*?\]', '', welcome_msg)
                welcome_msg = re.sub(r'\n\s*\n', '\n', welcome_msg)
            await bot.send_group_msg(
                group_id=event.group_id,
                message=await format_message(bot, welcome_msg, event.user_id, event.group_id)
            )
    except Exception as e:
        logger.error(f"发送欢迎消息失败: {e}")
        # 发送最简单的欢迎消息作为后备方案
        try:
            await bot.send_group_msg(
                group_id=event.group_id,
                message=f"欢迎 {MessageSegment.at(event.user_id)} 加入本群！"
            )
        except Exception as e2:
            logger.error(f"发送简单欢迎消息也失败了: {e2}")

@admin_msg.handle()
async def handle_admin_msg(event: GroupMessageEvent, bot: Bot):
    if not isinstance(event, GroupMessageEvent):
        return
        
    if not is_plugin_enabled(event.group_id):
        return
    
    if not (event.sender.role in ["admin", "owner"] or str(event.user_id) in bot.config.superusers):
        return
    
    # 不需要检查是否包含@，Rule已经处理
    at_users = [seg.data["qq"] for seg in event.message if seg.type == "at"]
    msg = event.raw_message
    
    for user_id in at_users:
        if "禁言" in msg or "ban" in msg.lower() or "mute" in msg.lower():
            duration_match = re.search(r"(?:禁言|ban|mute)\s*(\d+)", msg.lower())
            duration = int(duration_match.group(1)) if duration_match else 60
            
            # QQ群禁言API最大时长为10000分钟（约6.94天）
            MAX_MUTE_DURATION = 10000
            original_duration = duration
            
            if duration > MAX_MUTE_DURATION:
                duration = MAX_MUTE_DURATION
            
            try:
                await bot.set_group_ban(
                    group_id=event.group_id,
                    user_id=int(user_id),
                    duration=duration * 60
                )
                
                if original_duration > MAX_MUTE_DURATION:
                    days = MAX_MUTE_DURATION // 1440  # 1440 = 24 * 60 分钟/天
                    remaining_minutes = MAX_MUTE_DURATION % 1440
                    hours = remaining_minutes // 60
                    minutes = remaining_minutes % 60
                    time_str = f"{days}天{hours}小时{minutes}分钟"
                    await admin_msg.send(f"由于API限制，禁言时长已被限制为最大值：{time_str}")
                else:
                    if duration >= 1440:  # 大于等于1天的情况
                        days = duration // 1440
                        remaining_minutes = duration % 1440
                        hours = remaining_minutes // 60
                        minutes = remaining_minutes % 60
                        time_str = f"{days}天{hours}小时{minutes}分钟"
                        await admin_msg.send(f"已禁言 {user_id} {time_str}")
                    else:  # 小于1天的情况
                        await admin_msg.send(f"已禁言 {user_id} {duration}分钟")
            except Exception as e:
                if "timeout" in str(e):
                    await admin_msg.send(f"禁言失败：禁言时长过长，请设置不超过10000分钟")
                else:
                    await admin_msg.send(f"禁言失败：{e}")
        
        elif "解禁" in msg or "unban" in msg.lower() or "unmute" in msg.lower():
            try:
                await bot.set_group_ban(
                    group_id=event.group_id,
                    user_id=int(user_id),
                    duration=0
                )
                await admin_msg.send(f"已解禁 {user_id}")
            except Exception as e:
                await admin_msg.send(f"解禁失败：{e}")
        
        elif "踢" in msg or "kick" in msg.lower():
            try:
                # 先添加到黑名单
                db_path = data_dir / "group_manager.db"
                conn = sqlite3.connect(str(db_path))
                cursor = conn.cursor()
                
                effective_blacklist_gid = get_effective_blacklist_group_id(event.group_id, conn)
                # get_effective_blacklist_group_id might commit if share source was invalid

                cursor.execute(
                    "INSERT OR REPLACE INTO blacklist (group_id, user_id, reason, add_time) VALUES (?, ?, ?, ?)",
                    (effective_blacklist_gid, int(user_id), "管理员踢出", datetime.datetime.now())
                )
                conn.commit() # Commit the blacklist addition
                conn.close()
                
                # 再执行踢出操作
                await bot.set_group_kick(
                    group_id=event.group_id,
                    user_id=int(user_id),
                    reject_add_request=True
                )
                await admin_msg.send(f"O而K之\n已踢出 {user_id} 并加入群 {effective_blacklist_gid} 的黑名单OVO ~")
            except Exception as e:
                await admin_msg.send(f"踢出失败：{e}")

@group_tips.handle()
async def handle_group_tips(event: GroupMessageEvent, bot: Bot):
    # 消息类型由Rule处理，不需要再检查
    config = load_config(event.group_id)
    if not config["plugin_enabled"]:
        return
    
    try:
        # 检查是否为好友关系
        friend_list = await bot.get_friend_list()
        is_friend = any(friend["user_id"] == event.user_id for friend in friend_list)
        
        if not is_friend:
            # 发送非好友提示
            await bot.send_group_msg(
                group_id=event.group_id,
                message=await format_message(bot, config['not_friend_tip'], event.user_id, event.group_id)
            )
            return
        
        # 获取用户信息
        try:
            user_info = await bot.get_stranger_info(user_id=event.user_id)
            nickname = user_info.get("nickname", str(event.user_id))
        except Exception as e:
            logger.warning(f"获取用户信息失败: {e}")
            nickname = str(event.user_id)
        
        # 获取群信息
        try:
            group_info = await bot.get_group_info(group_id=event.group_id)
            member_list = await bot.get_group_member_list(group_id=event.group_id)
            member_count = len(member_list)
            group_name = group_info.get("group_name", str(event.group_id))
        except Exception as e:
            logger.warning(f"获取群信息失败: {e}")
            member_count = 0
            group_name = str(event.group_id)
        
        # 格式化消息
        msg = config['welcome_msg']
        msg = msg.replace("{at}", f"[CQ:at,qq={event.user_id}]")
        msg = msg.replace("{nickname}", nickname)
        msg = msg.replace("{user_id}", str(event.user_id))
        msg = msg.replace("{group_name}", group_name)
        msg = msg.replace("{member_count}", str(member_count))
        
        # 处理头像
        if "{avatar}" in msg:
            max_retries = 3
            retry_count = 0
            avatar_urls = [
                lambda uid: f"https://q1.qlogo.cn/g?b=qq&nk={uid}&s=640",
                lambda uid: f"https://q2.qlogo.cn/headimg_dl?dst_uin={uid}&spec=640",
                lambda uid: f"https://q.qlogo.cn/g?b=qq&nk={uid}&s=640"
            ]
            
            while retry_count < max_retries:
                try:
                    url_func = avatar_urls[min(retry_count, len(avatar_urls) - 1)]
                    avatar_url = url_func(event.user_id)
                    
                    # 构建头像消息，添加所有必要参数
                    avatar_msg = (
                        f"[CQ:image,file={avatar_url},"
                        f"cache=0,timeout=20,c=3,"
                        f"no_convert=1,headers={{\"User-Agent\":\"Mozilla/5.0\"}},"
                        f"subType=0]"
                    )
                    
                    msg = msg.replace("{avatar}", avatar_msg)
                    break
                except Exception as e:
                    retry_count += 1
                    logger.warning(f"头像处理第 {retry_count} 次失败: {e}")
                    await asyncio.sleep(0.5)
                    
                    if retry_count >= max_retries:
                        logger.warning("头像处理最终失败，移除头像标记")
                        msg = re.sub(r'{avatar}\n', '', msg)
                        msg = re.sub(r'{avatar}', '', msg)
                        msg = re.sub(r'\[CQ:image.*?\]', '', msg)
        
        # 清理多余的空行
        msg = re.sub(r'\n\s*\n', '\n', msg)
        msg = msg.strip()
        
        # 如果消息为空，使用默认消息
        if not msg:
            msg = f"欢迎 {MessageSegment.at(event.user_id)} 加入本群！"
        
        # 尝试私聊发送
        try:
            await bot.send_private_msg(
                user_id=event.user_id,
                message=msg
            )
            await bot.send_group_msg(
                group_id=event.group_id,
                message=f"已私聊发送群提示给 {MessageSegment.at(event.user_id)}"
            )
        except Exception as e:
            logger.error(f"私聊发送失败: {e}")
            # 如果私聊失败，尝试群发送
            await bot.send_group_msg(
                group_id=event.group_id,
                message=msg
            )
                
    except Exception as e:
        logger.error(f"处理群提示失败: {e}")
        try:
            await bot.send_group_msg(
                group_id=event.group_id,
                message=f"消息处理失败，请稍后重试。\n错误信息：{str(e)}"
            )
        except:
            pass

@copy_config.handle()
async def handle_copy_config(event: GroupMessageEvent, args: Message = CommandArg()):
    # 检查消息是否以正确的命令开头
    raw_msg = event.raw_message.strip()
    if not (raw_msg.startswith("#复制配置") or raw_msg.startswith("复制配置")):
        return
        
    if not is_plugin_enabled(event.group_id):
        await copy_config.finish("群管理功能未启用，请先使用 #群管理 开启")
        return
    
    args_list = args.extract_plain_text().strip().split()
    if len(args_list) != 2:
        await copy_config.finish("格式错误！请使用：#复制配置 [群号] [配置/黑名单]")
        return
    
    try:
        target_group_str = args_list[0]
        copy_type = args_list[1]

        if not target_group_str.isdigit():
            await copy_config.finish("群号必须是数字！")
            return
        target_group = int(target_group_str)
        
        if copy_type not in ["配置", "黑名单"]:
            await copy_config.finish("类型错误！只能复制 配置 或 黑名单")
            return
        
        db_path = data_dir / "group_manager.db"
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()
        
        source_config_exists = cursor.execute("SELECT 1 FROM group_config WHERE group_id = ?", (target_group,)).fetchone()
        if not source_config_exists:
            conn.close()
            await copy_config.finish(f"源群 {target_group} 的配置不存在，无法复制。")
            return
        
        if copy_type == "配置":
            cursor.execute("SELECT * FROM group_config WHERE group_id = ?", (target_group,))
            source_config = cursor.fetchone()
            
            # source_config should exist due to the check above, but as a safeguard:
            if not source_config:
                conn.close()
                await copy_config.finish(f"未能获取源群 {target_group} 的详细配置。") # Should not happen
                return
            
            cursor.execute("""
                UPDATE group_config 
                SET welcome_msg = ?, leave_msg = ?, auto_approve = ?, 
                    plugin_enabled = ?, min_qq_level = ?, private_chat = ?,
                    short_welcome_msg = ?, not_friend_tip = ?
                WHERE group_id = ?
            """, (
                source_config[1], source_config[2], source_config[3],
                source_config[4], source_config[5], source_config[6],
                source_config[9], source_config[10], # short_welcome_msg, not_friend_tip
                event.group_id
            ))
            
        else:  # 复制黑名单
            # Check if target group has any blacklist entries to copy
            cursor.execute("SELECT 1 FROM blacklist WHERE group_id = ?", (target_group,))
            if not cursor.fetchone():
                conn.close()
                await copy_config.finish(f"源群 {target_group} 黑名单为空，无需复制。")
                return

            cursor.execute("DELETE FROM blacklist WHERE group_id = ?", (event.group_id,))
            cursor.execute("""
                INSERT INTO blacklist (group_id, user_id, reason, add_time)
                SELECT ?, user_id, reason, add_time
                FROM blacklist WHERE group_id = ?
            """, (event.group_id, target_group))
        
        conn.commit()
        conn.close()
        
        await copy_config.finish(f"已成功复制群 {target_group} 的{copy_type}")
        
    except FinishedException:
        raise
    except ValueError: # This is now more specific due to isdigit check
        await copy_config.finish("群号格式处理时发生错误。")
    except sqlite3.Error as e:
        logger.error(f"复制配置数据库操作失败：{e}", exc_info=True)
        if 'conn' in locals() and conn:
            conn.close()
        await copy_config.finish("复制配置失败：数据库操作错误")
    except Exception as e:
        logger.error(f"复制配置时发生意外错误：{e}", exc_info=True)
        if 'conn' in locals() and conn:
            conn.close()
        await copy_config.finish("复制失败，发生未知错误，请检查后台日志")

@share_config.handle()
async def handle_share_config(event: GroupMessageEvent, args: Message = CommandArg()):
    # 检查消息是否以正确的命令开头
    raw_msg = event.raw_message.strip()
    if not (raw_msg.startswith("#共享配置") or raw_msg.startswith("共享配置")):
        return
        
    if not is_plugin_enabled(event.group_id):
        await share_config.finish("群管理功能未启用，请先使用 #群管理 开启")
        return
    
    args_list = args.extract_plain_text().strip().split()
    if len(args_list) != 2:
        await share_config.finish("格式错误！请使用：#共享配置 [群号] [配置/黑名单]")
        return
    
    try:
        target_group_str = args_list[0]
        share_type = args_list[1]

        if not target_group_str.isdigit():
            await share_config.finish("群号必须是数字！")
            return
        target_group = int(target_group_str)

        if target_group == event.group_id:
            await share_config.finish("不能将配置共享给本群自身。")
            return
        
        if share_type not in ["配置", "黑名单"]:
            await share_config.finish("类型错误！只能共享 配置 或 黑名单")
            return
        
        db_path = data_dir / "group_manager.db"
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()
        
        # 检查目标群是否存在于group_config表
        cursor.execute("SELECT 1 FROM group_config WHERE group_id = ?", (target_group,))
        if not cursor.fetchone():
            conn.close()
            await share_config.finish(f"目标共享群 {target_group} 不存在或未初始化配置。")
            return
        
        # 更新共享信息
        cursor.execute(
            "UPDATE group_config SET share_from = ?, share_type = ? WHERE group_id = ?",
            (target_group, share_type, event.group_id)
        )
        
        conn.commit()
        conn.close()
        
        await share_config.finish(f"已成功与群 {target_group} 共享{share_type}")
        
    except FinishedException:
        raise
    except ValueError: # This is now more specific
        await share_config.finish("群号格式处理时发生错误。")
    except sqlite3.Error as e:
        logger.error(f"共享配置数据库操作失败：{e}", exc_info=True)
        if 'conn' in locals() and conn:
            conn.close()
        await share_config.finish("共享配置失败：数据库操作错误")
    except Exception as e:
        logger.error(f"共享配置时发生意外错误：{e}", exc_info=True)
        if 'conn' in locals() and conn:
            conn.close()
        await share_config.finish("共享失败，发生未知错误，请检查后台日志")

@quit_share.handle()
async def handle_quit_share(event: GroupMessageEvent, args: Message = CommandArg()):
    if not is_plugin_enabled(event.group_id):
        await quit_share.finish("群管理功能未启用，请先使用 #群管理 开启")
        return
    
    share_type_arg = args.extract_plain_text().strip()
    # 检查参数是否正好是 "配置" 或 "黑名单"
    if share_type_arg not in ["配置", "黑名单"]:
        await quit_share.finish("类型错误！参数应为【配置】或【黑名单】。用法: #退出共享 [配置/黑名单]")
        return
    
    db_path = data_dir / "group_manager.db"
    conn = sqlite3.connect(str(db_path))
    cursor = conn.cursor()
    
    # 检查是否正在共享指定的类型
    cursor.execute(
        "SELECT share_from, share_type FROM group_config WHERE group_id = ? AND share_type = ?",
        (event.group_id, share_type_arg)
    )
    result = cursor.fetchone()
    
    if not result or not result[0]: # result[0] is share_from
        conn.close()
        await quit_share.finish(f"当前群未共享{share_type_arg}，无需退出。")
        return
    
    # 重置共享信息
    cursor.execute(
        "UPDATE group_config SET share_from = 0, share_type = NULL WHERE group_id = ? AND share_type = ?",
        (event.group_id, share_type_arg)
    )
    
    # 如果是配置共享，重置为默认配置 (根据原逻辑，此处 plugin_enabled 也应考虑是否重置，但保持原样)
    if share_type_arg == "配置":
        # 获取当前 plugin_enabled 状态，避免意外关闭插件
        cursor.execute("SELECT plugin_enabled FROM group_config WHERE group_id = ?", (event.group_id,))
        current_plugin_enabled = cursor.fetchone()[0] 
        cursor.execute(
            "UPDATE group_config SET welcome_msg = ?, leave_msg = ?, auto_approve = 0, min_qq_level = 0, private_chat = 0, short_welcome_msg = ?, not_friend_tip = ?, plugin_enabled = ? WHERE group_id = ?",
            (
                "{avatar}\n欢迎 {at} 加入本群！", 
                "{avatar}\n{nickname} {user_id}\n退出了群聊，已自动加入黑名单OVO ~",
                "欢迎 {at} 加入本群！\n添加机器人为好友后发送【群提示】可获取详细信息~",
                "{at} 您还不是机器人的好友，请先添加好友后再发送【群提示】获取详细信息~",
                current_plugin_enabled, # 保持插件启用状态不变
                event.group_id
            )
        )
    # 如果是黑名单共享，原逻辑是清空黑名单。这保持不变。
    # else: # share_type_arg == "黑名单"
    #     cursor.execute("DELETE FROM blacklist WHERE group_id = ?", (event.group_id,))
    
    conn.commit()
    conn.close()
    
    await quit_share.finish(f"已退出{share_type_arg}共享。相关设置已重置（如适用）。")

@restore_config.handle()
async def handle_restore_config(event: GroupMessageEvent):
    if not is_plugin_enabled(event.group_id):
        await restore_config.finish("群管理功能未启用，请先使用 #群管理 开启")
        return
    
    # 生成确认消息
    confirm_msg = (
        "⚠️ 警告：该操作将会：\n"
        "1. 还原所有配置为默认值\n"
        "2. 取消配置共享设置\n\n"
        "※ 该操作不可撤销！\n"
        "→ 确认请在1分钟内发送【确认还原配置】\n"
        "→ 取消请发送【取消还原配置】或等待1分钟"
    )
    
    # 记录确认信息
    restore_confirmations["config"][event.group_id] = {
        "user_id": event.user_id,
        "timestamp": datetime.datetime.now()
    }
    
    await restore_config.send(confirm_msg)

@restore_blacklist.handle()
async def handle_restore_blacklist(event: GroupMessageEvent):
    if not is_plugin_enabled(event.group_id):
        await restore_blacklist.finish("群管理功能未启用，请先使用 #群管理 开启")
        return
    
    # 生成确认消息
    confirm_msg = (
        "⚠️ 警告：该操作将会：\n"
        "1. 清空当前群的黑名单\n"
        "2. 取消黑名单共享设置\n\n"
        "※ 该操作不可撤销！\n"
        "→ 确认请在1分钟内发送【确认还原黑名单】\n"
        "→ 取消请发送【取消还原黑名单】或等待1分钟"
    )
    
    # 记录确认信息
    restore_confirmations["blacklist"][event.group_id] = {
        "user_id": event.user_id,
        "timestamp": datetime.datetime.now()
    }
    
    await restore_blacklist.send(confirm_msg)

@restore_confirm.handle()
async def handle_restore_confirm(event: GroupMessageEvent):
    msg = event.raw_message.strip()
    
    # 处理配置还原确认 (不再需要检查消息类型，由Rule处理)
    if event.group_id in restore_confirmations["config"]:
        confirm_info = restore_confirmations["config"][event.group_id]
        
        # 检查是否是原始请求者
        if event.user_id != confirm_info["user_id"]:
            return
        
        # 检查是否在1分钟内
        if (datetime.datetime.now() - confirm_info["timestamp"]).total_seconds() > 60:
            del restore_confirmations["config"][event.group_id]
            return
        
        if msg == "确认还原配置":
            try:
                db_path = data_dir / "group_manager.db"
                conn = sqlite3.connect(str(db_path))
                cursor = conn.cursor()
                
                # 还原配置为默认值
                cursor.execute("""
                    UPDATE group_config 
                    SET welcome_msg = ?, 
                        leave_msg = ?, 
                        auto_approve = 0,
                        plugin_enabled = 1,
                        min_qq_level = 0,
                        private_chat = 0,
                        share_from = 0,
                        share_type = NULL
                    WHERE group_id = ?
                """, (
                    "{avatar}\n欢迎 {at} 加入本群！",
                    "{avatar}\n{nickname} {user_id}\n退出了群聊，已自动加入黑名单OVO ~",
                    event.group_id
                ))
                
                conn.commit()
                conn.close()
                
                await restore_config.send("已成功还原所有配置为默认值")
            except Exception as e:
                logger.error(f"还原配置失败：{e}")
                await restore_config.send("还原配置失败，请稍后重试")
            finally:
                del restore_confirmations["config"][event.group_id]
        
        elif msg == "取消还原配置":
            await restore_config.send("已取消还原配置操作")
            del restore_confirmations["config"][event.group_id]
    
    # 处理黑名单还原确认
    elif event.group_id in restore_confirmations["blacklist"]:
        confirm_info = restore_confirmations["blacklist"][event.group_id]
        
        # 检查是否是原始请求者
        if event.user_id != confirm_info["user_id"]:
            return
        
        # 检查是否在1分钟内
        if (datetime.datetime.now() - confirm_info["timestamp"]).total_seconds() > 60:
            del restore_confirmations["blacklist"][event.group_id]
            return
        
        if msg == "确认还原黑名单":
            try:
                db_path = data_dir / "group_manager.db"
                conn = sqlite3.connect(str(db_path))
                cursor = conn.cursor()
                
                # 清空黑名单
                cursor.execute("DELETE FROM blacklist WHERE group_id = ?", (event.group_id,))
                
                # 取消黑名单共享
                cursor.execute("""
                    UPDATE group_config 
                    SET share_from = CASE 
                            WHEN share_type = '黑名单' THEN 0 
                            ELSE share_from 
                        END,
                        share_type = CASE 
                            WHEN share_type = '黑名单' THEN NULL 
                            ELSE share_type 
                        END
                    WHERE group_id = ?
                """, (event.group_id,))
                
                conn.commit()
                conn.close()
                
                await restore_blacklist.send("已成功清空黑名单")
            except Exception as e:
                logger.error(f"还原黑名单失败：{e}")
                await restore_blacklist.send("还原黑名单失败，请稍后重试")
            finally:
                del restore_confirmations["blacklist"][event.group_id]
        
        elif msg == "取消还原黑名单":
            await restore_blacklist.send("已取消还原黑名单操作")
            del restore_confirmations["blacklist"][event.group_id]

# 定时清理超时的确认信息
@scheduler.scheduled_job("interval", minutes=1, id="clean_restore_confirmations")
async def clean_restore_confirmations():
    now = datetime.datetime.now()
    
    # 清理配置还原确认
    expired_groups = []
    for group_id, confirm_info in restore_confirmations["config"].items():
        if (now - confirm_info["timestamp"]).total_seconds() > 60:
            expired_groups.append(group_id)
    for group_id in expired_groups:
        del restore_confirmations["config"][group_id]
    
    # 清理黑名单还原确认
    expired_groups = []
    for group_id, confirm_info in restore_confirmations["blacklist"].items():
        if (now - confirm_info["timestamp"]).total_seconds() > 60:
            expired_groups.append(group_id)
    for group_id in expired_groups:
        del restore_confirmations["blacklist"][group_id]

@query_blacklist.handle()
async def handle_query_blacklist(event: GroupMessageEvent, args: Message = CommandArg()):
    if not is_plugin_enabled(event.group_id):
        await query_blacklist.finish("群管理功能未启用，请先使用 #群管理 开启")
        return

    user_id_text = args.extract_plain_text().strip()
    if not user_id_text:
        await query_blacklist.finish("请提供要查询的QQ号")
        return
    
    try:
        user_id = int(user_id_text)
    except ValueError:
        await query_blacklist.finish("请提供正确的QQ号")
        return
    
    db_path = data_dir / "group_manager.db"
    conn = sqlite3.connect(str(db_path))
    cursor = conn.cursor()

    effective_blacklist_gid = get_effective_blacklist_group_id(event.group_id, conn)
    if cursor.connection.in_transaction:
        conn.commit() # 确保 get_effective_blacklist_group_id 中的更改被提交
    
    cursor.execute(
        "SELECT reason, add_time FROM blacklist WHERE group_id = ? AND user_id = ?",
        (effective_blacklist_gid, user_id)
    )
    result = cursor.fetchone()
    conn.close()
    
    if result:
        reason, add_time = result
        msg = [
            f"QQ号：{user_id}",
            f"状态：在黑名单中",
            f"加入原因：{reason}",
            f"加入时间：{add_time}"
        ]
    else:
        msg = [f"QQ号 {user_id} 不在黑名单中"]
    
    await query_blacklist.finish("\n".join(msg))

@show_reject.handle()
async def handle_show_reject(event: GroupMessageEvent):
    if not is_plugin_enabled(event.group_id):
        await show_reject.finish("群管理功能未启用，请先使用 #群管理 开启")
        return
    
    db_path = data_dir / "group_manager.db"
    conn = sqlite3.connect(str(db_path))
    cursor = conn.cursor()
    
    cursor.execute(
        "SELECT user_id, reason, reject_time, comment FROM reject_history WHERE group_id = ? ORDER BY reject_time DESC",
        (event.group_id,)
    )
    results = cursor.fetchall()
    
    # 获取总数
    cursor.execute("SELECT COUNT(*) FROM reject_history WHERE group_id = ?", (event.group_id,))
    total_count = cursor.fetchone()[0]
    
    conn.close()
    
    if not results:
        await show_reject.finish("暂无拒绝记录")
        return
    
    msg = ["=====拒绝记录列表====="]
    for user_id, reason, reject_time, comment in results:
        msg.append(f"QQ号：{user_id}")
        msg.append(f"原因：{reason}")
        msg.append(f"时间：{reject_time}")
        if comment:
            msg.append(f"验证信息：{comment}")
        msg.append("---")
    
    msg.append(f"\n共计 {total_count} 条拒绝记录")
    
    await show_reject.finish("\n".join(msg))

@query_reject.handle()
async def handle_query_reject(event: GroupMessageEvent, args: Message = CommandArg()):
    if not is_plugin_enabled(event.group_id):
        await query_reject.finish("群管理功能未启用，请先使用 #群管理 开启")
        return
    
    user_id_text = args.extract_plain_text().strip()
    if not user_id_text:
        await query_reject.finish("请提供要查询的QQ号")
        return
    
    try:
        user_id = int(user_id_text)
    except ValueError:
        await query_reject.finish("请提供正确的QQ号")
        return
    
    db_path = data_dir / "group_manager.db"
    conn = sqlite3.connect(str(db_path))
    cursor = conn.cursor()
    
    cursor.execute(
        "SELECT reason, reject_time, comment FROM reject_history WHERE group_id = ? AND user_id = ? ORDER BY reject_time DESC",
        (event.group_id, user_id)
    )
    results = cursor.fetchall()
    conn.close()
    
    if not results:
        await query_reject.finish(f"未找到 QQ号 {user_id} 的拒绝记录")
        return
    
    msg = [f"QQ号 {user_id} 的拒绝记录："]
    for reason, reject_time, comment in results:
        msg.append(f"原因：{reason}")
        msg.append(f"时间：{reject_time}")
        if comment:
            msg.append(f"验证信息：{comment}")
        msg.append("---")
    
    msg.append(f"\n共计 {len(results)} 条拒绝记录")
    
    await query_reject.finish("\n".join(msg))

@clear_reject.handle()
async def handle_clear_reject(event: GroupMessageEvent):
    if not is_plugin_enabled(event.group_id):
        await clear_reject.finish("群管理功能未启用，请先使用 #群管理 开启")
        return
    
    # 获取记录数量
    db_path = data_dir / "group_manager.db"
    conn = sqlite3.connect(str(db_path))
    cursor = conn.cursor()
    cursor.execute("SELECT COUNT(*) FROM reject_history WHERE group_id = ?", (event.group_id,))
    count = cursor.fetchone()[0]
    conn.close()
    
    # 生成确认消息
    confirm_msg = (
        "⚠️ 警告：该操作将会：\n"
        f"1. 清空当前群的所有拒绝记录（{count} 条）\n\n"
        "※ 该操作不可撤销！\n"
        "→ 确认请在1分钟内发送【确认清空记录】\n"
        "→ 取消请发送【取消清空记录】或等待1分钟"
    )
    
    # 记录确认信息
    restore_confirmations["reject"][event.group_id] = {
        "user_id": event.user_id,
        "timestamp": datetime.datetime.now()
    }
    
    await clear_reject.send(confirm_msg)

@clear_reject_confirm.handle()
async def handle_clear_reject_confirm(event: GroupMessageEvent):
    msg = event.raw_message.strip()
    
    # 处理拒绝记录确认 (不再需要检查消息类型，由Rule处理)
    if event.group_id in restore_confirmations["reject"]:
        confirm_info = restore_confirmations["reject"][event.group_id]
        
        # 检查是否是原始请求者
        if event.user_id != confirm_info["user_id"]:
            return
        
        # 检查是否在1分钟内
        if (datetime.datetime.now() - confirm_info["timestamp"]).total_seconds() > 60:
            del restore_confirmations["reject"][event.group_id]
            return
        
        if msg == "确认清空记录":
            try:
                db_path = data_dir / "group_manager.db"
                conn = sqlite3.connect(str(db_path))
                cursor = conn.cursor()
                
                cursor.execute("DELETE FROM reject_history WHERE group_id = ?", (event.group_id,))
                count = cursor.rowcount
                
                conn.commit()
                conn.close()
                
                await clear_reject.send(f"已清空 {count} 条拒绝记录")
            except Exception as e:
                logger.error(f"清空拒绝记录失败：{e}")
                await clear_reject.send("清空记录失败，请稍后重试")
            finally:
                del restore_confirmations["reject"][event.group_id]
        
        elif msg == "取消清空记录":
            await clear_reject.send("已取消清空记录操作")
            del restore_confirmations["reject"][event.group_id]

@set_short_welcome.handle()
async def handle_set_short_welcome(event: GroupMessageEvent, bot: Bot, args: Message = CommandArg()):
    if not is_plugin_enabled(event.group_id):
        await set_short_welcome.finish("群管理功能未启用，请先使用 #群管理 开启")
        return

    msg = str(event.message).strip()
    if msg.startswith("#设置简短欢迎语"):
        msg = msg[8:].strip()
    
    if not msg:
        await set_short_welcome.finish("请输入简短欢迎语！")
        return
    
    db_path = data_dir / "group_manager.db"
    conn = sqlite3.connect(str(db_path))
    cursor = conn.cursor()
    
    cursor.execute(
        "UPDATE group_config SET short_welcome_msg = ? WHERE group_id = ?",
        (msg, event.group_id)
    )
    conn.commit()
    conn.close()
    
    preview = await format_message(bot, msg, event.user_id, event.group_id)
    await set_short_welcome.finish(f"简短欢迎语设置成功！\n预览效果：\n{preview}")

@show_short_welcome.handle()
async def handle_show_short_welcome(event: GroupMessageEvent, bot: Bot):
    config = load_config(event.group_id)
    preview = await format_message(bot, config['short_welcome_msg'], event.user_id, event.group_id)
    await show_short_welcome.finish(f"当前简短欢迎语：\n{config['short_welcome_msg']}\n\n预览效果：\n{preview}")

@set_not_friend_tip.handle()
async def handle_set_not_friend_tip(event: GroupMessageEvent, bot: Bot, args: Message = CommandArg()):
    if not is_plugin_enabled(event.group_id):
        await set_not_friend_tip.finish("群管理功能未启用，请先使用 #群管理 开启")
        return

    msg = str(event.message).strip()
    if msg.startswith("#设置非好友提示语"):
        msg = msg[9:].strip()
    
    if not msg:
        await set_not_friend_tip.finish("请输入非好友提示语！")
        return
    
    db_path = data_dir / "group_manager.db"
    conn = sqlite3.connect(str(db_path))
    cursor = conn.cursor()
    
    cursor.execute(
        "UPDATE group_config SET not_friend_tip = ? WHERE group_id = ?",
        (msg, event.group_id)
    )
    conn.commit()
    conn.close()
    
    preview = await format_message(bot, msg, event.user_id, event.group_id)
    await set_not_friend_tip.finish(f"非好友提示语设置成功！\n预览效果：\n{preview}")

@show_not_friend_tip.handle()
async def handle_show_not_friend_tip(event: GroupMessageEvent, bot: Bot):
    config = load_config(event.group_id)
    preview = await format_message(bot, config['not_friend_tip'], event.user_id, event.group_id)
    await show_not_friend_tip.finish(f"当前非好友提示语：\n{config['not_friend_tip']}\n\n预览效果：\n{preview}")

show_blacklist_ids = on_command("查看黑名单账号", permission=ADMIN_PERMISSION, priority=5, block=True)

@show_blacklist_ids.handle()
async def handle_show_blacklist_ids(event: GroupMessageEvent):
    if not is_plugin_enabled(event.group_id):
        await show_blacklist_ids.finish("群管理功能未启用，请先使用 #群管理 开启")
        return

    db_path = data_dir / "group_manager.db"
    conn = sqlite3.connect(str(db_path))
    cursor = conn.cursor()

    effective_blacklist_gid = get_effective_blacklist_group_id(event.group_id, conn)
    if cursor.connection.in_transaction:
         conn.commit() # 确保 get_effective_blacklist_group_id 中的更改被提交
    
    cursor.execute(
        "SELECT user_id FROM blacklist WHERE group_id = ? ORDER BY add_time DESC",
        (effective_blacklist_gid,)
    )
    results = cursor.fetchall()
    conn.close()
    
    if not results:
        await show_blacklist_ids.finish("黑名单为空")
        return
    
    msg = ["=====黑名单账号列表====="]
    for user_id, in results:
        msg.append(str(user_id))
    
    msg.append(f"\n共计 {len(results)} 个账号")
    
    await show_blacklist_ids.finish("\n".join(msg))

# 组管理命令
create_cluster = on_command("创建组", permission=ADMIN_PERMISSION, priority=5, block=True)
delete_cluster = on_command("删除组", permission=ADMIN_PERMISSION, priority=5, block=True)
join_cluster_cmd = on_command("加入组", permission=ADMIN_PERMISSION, priority=5, block=True)
quit_cluster_cmd = on_command("退出组", permission=ADMIN_PERMISSION, priority=5, block=True)
show_cluster_info = on_command("查看组信息", permission=ADMIN_PERMISSION, priority=5, block=True)
show_all_clusters = on_command("查看所有组", permission=ADMIN_PERMISSION, priority=5, block=True)
reinit_cluster = on_command("重新初始化组", permission=ADMIN_PERMISSION, priority=5, block=True)

# ===== 组管理命令处理函数 =====

@create_cluster.handle()
async def handle_create_cluster(event: GroupMessageEvent, args: Message = CommandArg()):
    if not is_plugin_enabled(event.group_id):
        await create_cluster.finish("群管理功能未启用，请先使用 #群管理 开启")
        return

    args_text = args.extract_plain_text().strip()
    if not args_text:
        await create_cluster.finish("请输入组名！\n格式：#创建组 [组名] [组描述(可选)]")
        return
    
    # 解析参数
    parts = args_text.split(maxsplit=1)
    cluster_name = parts[0]
    description = parts[1] if len(parts) > 1 else ""
    
    # 验证组名
    if len(cluster_name) > 20:
        await create_cluster.finish("组名长度不能超过20个字符")
        return
    
    if not cluster_name.replace("_", "").replace("-", "").isalnum():
        await create_cluster.finish("组名只能包含字母、数字、下划线和连字符")
        return
    
    # 创建组
    success, message = do_create_cluster(cluster_name, description, event.user_id)
    await create_cluster.finish(message)

@delete_cluster.handle()
async def handle_delete_cluster(event: GroupMessageEvent, args: Message = CommandArg()):
    if not is_plugin_enabled(event.group_id):
        await delete_cluster.finish("群管理功能未启用，请先使用 #群管理 开启")
        return

    cluster_name = args.extract_plain_text().strip()
    if not cluster_name:
        await delete_cluster.finish("请输入要删除的组名！\n格式：#删除组 [组名]")
        return
    
    # 删除组
    success, message = do_delete_cluster(cluster_name)
    await delete_cluster.finish(message)

@join_cluster_cmd.handle()
async def handle_join_cluster_cmd(event: GroupMessageEvent, bot: Bot, args: Message = CommandArg()):
    if not is_plugin_enabled(event.group_id):
        await join_cluster_cmd.finish("群管理功能未启用，请先使用 #群管理 开启")
        return

    cluster_name = args.extract_plain_text().strip()
    if not cluster_name:
        await join_cluster_cmd.finish("请输入要加入的组名！\n格式：#加入组 [组名]")
        return
    
    # 加入组
    success, message = do_join_cluster(event.group_id, cluster_name)
    
    if success:
        # 如果成功加入组，初始化现有群成员
        try:
            # 获取组ID
            db_path = data_dir / "group_manager.db"
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()
            cursor.execute("SELECT cluster_id FROM group_config WHERE group_id = ?", (event.group_id,))
            result = cursor.fetchone()
            conn.close()
            
            if result and result[0]:
                cluster_id = result[0]
                init_success, init_message = await initialize_existing_users(bot, event.group_id, cluster_id)
                
                if init_success:
                    message += f"\n{init_message}"
                else:
                    message += f"\n警告：{init_message}"
                    logger.warning(f"群 {event.group_id} 加入组 {cluster_name} 成功，但初始化现有用户失败：{init_message}")
            
        except Exception as e:
            logger.error(f"初始化现有用户时发生错误: {e}")
            message += f"\n警告：初始化现有群成员时发生错误，但群聊已成功加入组"
    
    await join_cluster_cmd.finish(message)

@quit_cluster_cmd.handle()
async def handle_quit_cluster_cmd(event: GroupMessageEvent):
    if not is_plugin_enabled(event.group_id):
        await quit_cluster_cmd.finish("群管理功能未启用，请先使用 #群管理 开启")
        return
    
    # 退出组
    success, message = do_quit_cluster(event.group_id)
    await quit_cluster_cmd.finish(message)

@show_cluster_info.handle()
async def handle_show_cluster_info(event: GroupMessageEvent, bot: Bot, args: Message = CommandArg()):
    if not is_plugin_enabled(event.group_id):
        await show_cluster_info.finish("群管理功能未启用，请先使用 #群管理 开启")
        return

    cluster_name = args.extract_plain_text().strip()
    
    # 如果没有指定组名，显示当前群所在的组信息
    if not cluster_name:
        db_path = data_dir / "group_manager.db"
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()
        
        try:
            cursor.execute("SELECT cluster_id FROM group_config WHERE group_id = ?", (event.group_id,))
            result = cursor.fetchone()
            if not result or not result[0]:
                await show_cluster_info.finish("当前群聊不在任何组中")
                return
            
            cluster_info = await get_cluster_info(bot=bot, cluster_id=result[0])
        except Exception as e:
            logger.error(f"查询组信息失败: {e}")
            await show_cluster_info.finish("查询组信息失败")
            return
        finally:
            conn.close()
    else:
        cluster_info = await get_cluster_info(bot=bot, cluster_name=cluster_name)
    
    if not cluster_info:
        await show_cluster_info.finish(f"组 '{cluster_name}' 不存在" if cluster_name else "查询组信息失败")
        return
    
    # 格式化组信息
    msg = [f"=====组信息：{cluster_info['cluster_name']}====="]
    msg.append(f"组ID：{cluster_info['cluster_id']}")
    if cluster_info['description']:
        msg.append(f"描述：{cluster_info['description']}")
    msg.append(f"创建时间：{cluster_info['created_time']}")
    msg.append(f"创建者：{cluster_info['created_by']}")
    msg.append(f"群聊数量：{len(cluster_info['groups'])}")
    msg.append(f"组群人数：{cluster_info['member_count']}")
    
    if cluster_info['groups']:
        msg.append("\n组内群聊：")
        for group_id in cluster_info['groups']:
            msg.append(f"  {group_id}")
    
    await show_cluster_info.finish("\n".join(msg))

@show_all_clusters.handle()
async def handle_show_all_clusters(event: GroupMessageEvent, bot: Bot):
    if not is_plugin_enabled(event.group_id):
        await show_all_clusters.finish("群管理功能未启用，请先使用 #群管理 开启")
        return

    clusters = await get_all_clusters(bot=bot)
    
    if not clusters:
        await show_all_clusters.finish("暂无任何组")
        return
    
    msg = ["=====所有组列表====="]
    for cluster in clusters:
        msg.append(f"组名：{cluster['cluster_name']}")
        msg.append(f"  ID：{cluster['cluster_id']}")
        if cluster['description']:
            msg.append(f"  描述：{cluster['description']}")
        msg.append(f"  群聊数：{cluster['group_count']}")
        msg.append(f"  组群人数：{cluster['member_count']}")
        msg.append(f"  创建时间：{cluster['created_time']}")
        msg.append("---")
    
    msg.append(f"\n共计 {len(clusters)} 个组")
    
    await show_all_clusters.finish("\n".join(msg))

@reinit_cluster.handle()
async def handle_reinit_cluster(event: GroupMessageEvent, bot: Bot):
    """重新初始化组的用户状态"""
    if not is_plugin_enabled(event.group_id):
        await reinit_cluster.finish("群管理功能未启用，请先使用 #群管理 开启")
        return

    # 检查当前群是否在组中
    db_path = data_dir / "group_manager.db"
    conn = sqlite3.connect(str(db_path))
    cursor = conn.cursor()
    
    try:
        cursor.execute("SELECT cluster_id FROM group_config WHERE group_id = ?", (event.group_id,))
        result = cursor.fetchone()
        if not result or not result[0]:
            await reinit_cluster.finish("当前群聊不在任何组中")
            return
        
        cluster_id = result[0]
        
        # 获取组名
        cursor.execute("SELECT cluster_name FROM group_clusters WHERE cluster_id = ?", (cluster_id,))
        cluster_name_result = cursor.fetchone()
        cluster_name = cluster_name_result[0] if cluster_name_result else f"组{cluster_id}"
        
        # 获取组内所有群聊
        cursor.execute("SELECT group_id FROM group_config WHERE cluster_id = ?", (cluster_id,))
        group_results = cursor.fetchall()
        
        if not group_results:
            await reinit_cluster.finish("组内没有群聊")
            return
        
        # 清理现有的跟踪记录
        cursor.execute("DELETE FROM cluster_user_tracking WHERE cluster_id = ?", (cluster_id,))
        conn.commit()
        
        # 获取机器人自己的ID
        try:
            bot_info = await bot.get_login_info()
            bot_user_id = bot_info["user_id"]
        except Exception as e:
            logger.warning(f"获取机器人信息失败: {e}")
            bot_user_id = None
        
        total_users = 0
        processed_users = set()
        
        # 重新初始化所有群聊的用户
        for group_row in group_results:
            group_id = group_row[0]
            try:
                # 获取群成员列表
                member_list = await bot.get_group_member_list(group_id=group_id)
                
                for member in member_list:
                    user_id = member["user_id"]
                    
                    # 跳过机器人自己
                    if bot_user_id and user_id == bot_user_id:
                        continue
                    
                    # 如果用户已经在其他群中处理过，跳过（避免重复）
                    if user_id in processed_users:
                        logger.info(f"用户 {user_id} 已在组内其他群中，跳过重复处理")
                        continue
                    
                    # 添加用户到跟踪表
                    cursor.execute(
                        "INSERT INTO cluster_user_tracking (cluster_id, user_id, group_id, status) VALUES (?, ?, ?, ?)",
                        (cluster_id, user_id, group_id, 'active')
                    )
                    processed_users.add(user_id)
                    total_users += 1
                    
            except Exception as e:
                logger.error(f"处理群 {group_id} 时失败: {e}")
                continue
        
        conn.commit()
        
        await reinit_cluster.finish(
            f"重新初始化完成！\n"
            f"组名：{cluster_name}\n"
            f"处理群聊数：{len(group_results)}\n"
            f"初始化用户数：{total_users}\n"
            f"※ 已自动去重，每个用户只记录在最先处理的群中"
        )
        
    except Exception as e:
        logger.error(f"重新初始化组失败: {e}")
        await reinit_cluster.finish(f"重新初始化失败：{e}")
    finally:
        conn.close()

# ===== 发言管理功能处理器 =====

@speech_monitor.handle()
async def handle_speech_monitor(event: GroupMessageEvent, bot: Bot):
    """处理发言监控"""
    try:
        logger.info(f"发言监控处理器被触发 - 群:{event.group_id} 用户:{event.user_id} 消息:'{event.raw_message}'")

        # 检查消息是否包含违规词
        has_violation, violation_word, action, punishment_type, punishment_duration, violation_threshold = check_message_for_violations(
            event.group_id, event.raw_message
        )

        logger.info(f"违规检测结果 - 群:{event.group_id} 违规:{has_violation} 违规词:{violation_word}")

        if not has_violation:
            return

        # 记录违规行为
        record_violation(
            event.group_id,
            event.user_id,
            event.message_id,
            violation_word,
            event.raw_message,
            action
        )

        # 撤回消息
        try:
            await bot.delete_msg(message_id=event.message_id)
            logger.info(f"已撤回用户 {event.user_id} 在群 {event.group_id} 的违规消息")
        except Exception as e:
            logger.error(f"撤回消息失败: {e}")

        # 获取用户违规次数
        violation_count = get_user_violation_count(event.group_id, event.user_id)

        # 使用违规词的独立配置，如果没有则使用全局配置
        if punishment_type is None or punishment_duration is None or violation_threshold is None:
            config = get_speech_monitor_config(event.group_id)
            punishment_type = punishment_type or config['punishment_type']
            punishment_duration = punishment_duration or config['punishment_duration']
            violation_threshold = violation_threshold or config['threshold']

        # 发送警告消息
        warning_msg = f"⚠️ 检测到违规内容已撤回\n"
        warning_msg += f"违规用户：{MessageSegment.at(event.user_id)}\n"
        warning_msg += f"违规词：{violation_word}\n"
        warning_msg += f"违规次数：{violation_count}/{violation_threshold}"

        # 检查是否需要处罚
        if violation_count >= violation_threshold:
            if punishment_type == 'mute':
                try:
                    await bot.set_group_ban(
                        group_id=event.group_id,
                        user_id=event.user_id,
                        duration=punishment_duration * 60
                    )
                    warning_msg += f"\n🔇 已执行禁言处罚：{punishment_duration}分钟"
                except Exception as e:
                    logger.error(f"禁言处罚失败: {e}")
                    warning_msg += f"\n❌ 禁言处罚失败：{e}"

            elif punishment_type == 'kick':
                try:
                    await bot.set_group_kick(
                        group_id=event.group_id,
                        user_id=event.user_id,
                        reject_add_request=False
                    )
                    warning_msg += f"\n👢 已执行踢出处罚"
                except Exception as e:
                    logger.error(f"踢出处罚失败: {e}")
                    warning_msg += f"\n❌ 踢出处罚失败：{e}"
        else:
            remaining = violation_threshold - violation_count
            warning_msg += f"\n再违规 {remaining} 次将被处罚"

        # 发送警告消息
        await bot.send_group_msg(
            group_id=event.group_id,
            message=warning_msg
        )

    except Exception as e:
        logger.error(f"发言监控处理失败: {e}")

@toggle_speech_monitor.handle()
async def handle_toggle_speech_monitor(event: GroupMessageEvent, args: Message = CommandArg()):
    if not is_plugin_enabled(event.group_id):
        await toggle_speech_monitor.finish("群管理功能未启用，请先使用 #群管理 开启")
        return

    status = args.extract_plain_text().strip()
    if status not in ["开启", "关闭"]:
        await toggle_speech_monitor.finish("请使用 开启/关闭")

    # 检查是否有配置共享
    shares = get_group_shares(event.group_id)
    effective_group_id = shares.get('配置', event.group_id)

    enabled = 1 if status == "开启" else 0
    success = update_speech_monitor_config(event.group_id, speech_monitor_enabled=enabled)

    if success:
        if effective_group_id != event.group_id:
            await toggle_speech_monitor.finish(f"发言监控功能已{status}\n（配置已更新到源群 {effective_group_id}）")
        else:
            await toggle_speech_monitor.finish(f"发言监控功能已{status}")
    else:
        await toggle_speech_monitor.finish("设置失败，请稍后重试")

@add_banned_word_cmd.handle()
async def handle_add_banned_word(event: GroupMessageEvent, args: Message = CommandArg()):
    if not is_plugin_enabled(event.group_id):
        await add_banned_word_cmd.finish("群管理功能未启用，请先使用 #群管理 开启")
        return

    words_text = args.extract_plain_text().strip()
    if not words_text:
        await add_banned_word_cmd.finish("请提供要添加的违规词（支持换行分隔多个违规词）")
        return

    # 支持换行和空格分隔
    words_input = words_text.replace('\n', ' ').split()
    if not words_input:
        await add_banned_word_cmd.finish("请提供有效的违规词")
        return

    success_words = []
    failed_words = []

    for word in words_input:
        word = word.strip()
        if not word:
            continue

        if add_banned_word(event.group_id, word, 'recall', event.user_id):
            success_words.append(word)
        else:
            failed_words.append(word)

    msg_parts = []
    if success_words:
        msg_parts.append(f"成功添加 {len(success_words)} 个违规词：{', '.join(success_words)}")
    if failed_words:
        msg_parts.append(f"添加失败 {len(failed_words)} 个违规词：{', '.join(failed_words)}")

    if not msg_parts:
        await add_banned_word_cmd.finish("没有有效的违规词进行操作")
    else:
        await add_banned_word_cmd.finish("\n".join(msg_parts))

@remove_banned_word_cmd.handle()
async def handle_remove_banned_word(event: GroupMessageEvent, args: Message = CommandArg()):
    if not is_plugin_enabled(event.group_id):
        await remove_banned_word_cmd.finish("群管理功能未启用，请先使用 #群管理 开启")
        return

    words_text = args.extract_plain_text().strip()
    if not words_text:
        await remove_banned_word_cmd.finish("请提供要移除的违规词（支持换行分隔多个违规词）")
        return

    # 支持换行和空格分隔
    words_input = words_text.replace('\n', ' ').split()
    if not words_input:
        await remove_banned_word_cmd.finish("请提供有效的违规词")
        return

    success_words = []
    failed_words = []

    for word in words_input:
        word = word.strip()
        if not word:
            continue

        if remove_banned_word(event.group_id, word):
            success_words.append(word)
        else:
            failed_words.append(word)

    msg_parts = []
    if success_words:
        msg_parts.append(f"成功移除 {len(success_words)} 个违规词：{', '.join(success_words)}")
    if failed_words:
        msg_parts.append(f"移除失败 {len(failed_words)} 个违规词：{', '.join(failed_words)}（可能不存在）")

    if not msg_parts:
        await remove_banned_word_cmd.finish("没有有效的违规词进行操作")
    else:
        await remove_banned_word_cmd.finish("\n".join(msg_parts))

@list_banned_words.handle()
async def handle_list_banned_words(event: GroupMessageEvent):
    if not is_plugin_enabled(event.group_id):
        await list_banned_words.finish("群管理功能未启用，请先使用 #群管理 开启")
        return

    banned_words = get_banned_words(event.group_id)

    if not banned_words:
        await list_banned_words.finish("当前群组没有设置违规词")
        return

    # 检查是否有共享
    shares = get_group_shares(event.group_id)
    share_info = ""
    if "违规词" in shares:
        share_info = f"（共享自群 {shares['违规词']}）"

    msg = f"当前群组违规词列表{share_info}（共 {len(banned_words)} 个）：\n"
    for i, word_data in enumerate(banned_words, 1):
        word = word_data[0]
        action = word_data[1]
        punishment_type = word_data[2] if len(word_data) > 2 else 'mute'
        punishment_duration = word_data[3] if len(word_data) > 3 else 60
        violation_threshold = word_data[4] if len(word_data) > 4 else 3

        punishment_type_cn = "禁言" if punishment_type == 'mute' else "踢出"
        duration_text = f"{punishment_duration}分钟" if punishment_type == 'mute' else ""

        msg += f"{i}. {word}\n"
        msg += f"   处罚：{punishment_type_cn}{duration_text} | 阈值：{violation_threshold}次\n"

    await list_banned_words.finish(msg.strip())

@set_violation_threshold.handle()
async def handle_set_violation_threshold(event: GroupMessageEvent, args: Message = CommandArg()):
    if not is_plugin_enabled(event.group_id):
        await set_violation_threshold.finish("群管理功能未启用，请先使用 #群管理 开启")
        return

    threshold_text = args.extract_plain_text().strip()
    if not threshold_text:
        await set_violation_threshold.finish("请提供违规阈值（数字）")
        return

    try:
        threshold = int(threshold_text)
        if threshold <= 0:
            await set_violation_threshold.finish("违规阈值必须大于0")
            return
    except ValueError:
        await set_violation_threshold.finish("请提供有效的数字")
        return

    success = update_speech_monitor_config(event.group_id, violation_threshold=threshold)

    if success:
        await set_violation_threshold.finish(f"违规阈值已设置为 {threshold} 次")
    else:
        await set_violation_threshold.finish("设置失败，请稍后重试")

@set_punishment.handle()
async def handle_set_punishment(event: GroupMessageEvent, args: Message = CommandArg()):
    if not is_plugin_enabled(event.group_id):
        await set_punishment.finish("群管理功能未启用，请先使用 #群管理 开启")
        return

    args_text = args.extract_plain_text().strip()
    if not args_text:
        await set_punishment.finish("请提供处罚方式和时长，格式：禁言/踢出 [时长(分钟)]")
        return

    parts = args_text.split()
    if len(parts) < 1:
        await set_punishment.finish("请提供处罚方式：禁言 或 踢出")
        return

    punishment_type = parts[0]
    if punishment_type not in ["禁言", "踢出"]:
        await set_punishment.finish("处罚方式只能是：禁言 或 踢出")
        return

    # 转换为英文存储
    punishment_type_en = 'mute' if punishment_type == '禁言' else 'kick'

    # 处理时长
    duration = 60  # 默认60分钟
    if len(parts) > 1:
        try:
            duration = int(parts[1])
            if duration <= 0:
                await set_punishment.finish("时长必须大于0分钟")
                return
        except ValueError:
            await set_punishment.finish("请提供有效的时长（分钟）")
            return

    success = update_speech_monitor_config(
        event.group_id,
        punishment_type=punishment_type_en,
        punishment_duration=duration
    )

    if success:
        if punishment_type == '踢出':
            await set_punishment.finish(f"处罚方式已设置为：{punishment_type}")
        else:
            await set_punishment.finish(f"处罚方式已设置为：{punishment_type} {duration}分钟")
    else:
        await set_punishment.finish("设置失败，请稍后重试")

@check_user_violations.handle()
async def handle_check_user_violations(event: GroupMessageEvent, args: Message = CommandArg()):
    if not is_plugin_enabled(event.group_id):
        await check_user_violations.finish("群管理功能未启用，请先使用 #群管理 开启")
        return

    user_id_text = args.extract_plain_text().strip()

    db_path = data_dir / "group_manager.db"
    conn = sqlite3.connect(str(db_path))
    cursor = conn.cursor()

    try:
        if user_id_text:
            # 查询指定用户的违规记录
            try:
                target_user_id = int(user_id_text)
            except ValueError:
                await check_user_violations.finish("请提供有效的QQ号")
                return

            # 获取违规次数
            violation_count = get_user_violation_count(event.group_id, target_user_id)

            # 获取最近的违规记录
            cursor.execute(
                "SELECT violation_word, violation_time FROM violation_records WHERE group_id = ? AND user_id = ? ORDER BY violation_time DESC LIMIT 10",
                (event.group_id, target_user_id)
            )
            records = cursor.fetchall()

            if violation_count == 0:
                await check_user_violations.finish(f"用户 {target_user_id} 没有违规记录")
                return

            msg = f"用户 {target_user_id} 的违规记录：\n"
            msg += f"总违规次数：{violation_count}\n\n"
            msg += "最近违规记录：\n"

            for i, (word, time) in enumerate(records, 1):
                msg += f"{i}. 违规词：{word} | 时间：{time}\n"

            await check_user_violations.finish(msg.strip())
        else:
            # 查询所有用户的违规统计
            cursor.execute(
                "SELECT user_id, violation_count, last_violation_time FROM violation_counts WHERE group_id = ? ORDER BY violation_count DESC LIMIT 20",
                (event.group_id,)
            )
            records = cursor.fetchall()

            if not records:
                await check_user_violations.finish("当前群组没有违规记录")
                return

            msg = f"群组违规记录统计（前20名）：\n"
            for i, (user_id, count, last_time) in enumerate(records, 1):
                msg += f"{i}. {user_id} | 违规{count}次 | 最后违规：{last_time}\n"

            await check_user_violations.finish(msg.strip())

    except Exception as e:
        logger.error(f"查询违规记录失败: {e}")
        await check_user_violations.finish("查询失败，请稍后重试")
    finally:
        conn.close()

@reset_violations.handle()
async def handle_reset_violations(event: GroupMessageEvent, args: Message = CommandArg()):
    if not is_plugin_enabled(event.group_id):
        await reset_violations.finish("群管理功能未启用，请先使用 #群管理 开启")
        return

    user_id_text = args.extract_plain_text().strip()
    if not user_id_text:
        await reset_violations.finish("请提供要重置的用户QQ号")
        return

    try:
        target_user_id = int(user_id_text)
    except ValueError:
        await reset_violations.finish("请提供有效的QQ号")
        return

    # 检查用户是否有违规记录
    violation_count = get_user_violation_count(event.group_id, target_user_id)
    if violation_count == 0:
        await reset_violations.finish(f"用户 {target_user_id} 没有违规记录")
        return

    success = reset_user_violations(event.group_id, target_user_id)

    if success:
        await reset_violations.finish(f"已重置用户 {target_user_id} 的违规记录（原违规次数：{violation_count}）")
    else:
        await reset_violations.finish("重置失败，请稍后重试")

@speech_monitor_config.handle()
async def handle_speech_monitor_config(event: GroupMessageEvent):
    if not is_plugin_enabled(event.group_id):
        await speech_monitor_config.finish("群管理功能未启用，请先使用 #群管理 开启")
        return

    config = get_speech_monitor_config(event.group_id)
    banned_words = get_banned_words(event.group_id)

    status = "开启" if config['enabled'] else "关闭"
    punishment_type_cn = "禁言" if config['punishment_type'] == 'mute' else "踢出"

    msg = f"发言监控配置：\n"
    msg += f"状态：{status}\n"
    msg += f"违规阈值：{config['threshold']} 次\n"
    msg += f"处罚方式：{punishment_type_cn}"

    if config['punishment_type'] == 'mute':
        msg += f" {config['punishment_duration']}分钟"

    msg += f"\n违规词数量：{len(banned_words)} 个"

    if banned_words:
        msg += f"\n违规词列表：{', '.join([word for word, _ in banned_words])}"

    await speech_monitor_config.finish(msg)

# ===== 多类型共享功能处理器 =====

@share_multi_config.handle()
async def handle_share_multi_config(event: GroupMessageEvent, args: Message = CommandArg()):
    if not is_plugin_enabled(event.group_id):
        await share_multi_config.finish("群管理功能未启用，请先使用 #群管理 开启")
        return

    args_text = args.extract_plain_text().strip()
    if not args_text:
        await share_multi_config.finish("格式错误！请使用：#共享多配置 [群号] [类型1,类型2...]")
        return

    parts = args_text.split(None, 1)
    if len(parts) != 2:
        await share_multi_config.finish("格式错误！请使用：#共享多配置 [群号] [类型1,类型2...]")
        return

    try:
        target_group = int(parts[0])
        share_types = [t.strip() for t in parts[1].split(',')]

        if target_group == event.group_id:
            await share_multi_config.finish("不能将配置共享给本群自身")
            return

        valid_types = ["配置", "黑名单", "违规词"]
        invalid_types = [t for t in share_types if t not in valid_types]
        if invalid_types:
            await share_multi_config.finish(f"无效的共享类型：{', '.join(invalid_types)}\n支持的类型：{', '.join(valid_types)}")
            return

        # 检查目标群是否存在
        db_path = data_dir / "group_manager.db"
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()

        cursor.execute("SELECT 1 FROM group_config WHERE group_id = ?", (target_group,))
        if not cursor.fetchone():
            conn.close()
            await share_multi_config.finish(f"目标群 {target_group} 不存在或未初始化配置")
            return

        # 设置多个共享
        success_types = []
        failed_types = []

        for share_type in share_types:
            if set_group_share(event.group_id, share_type, target_group):
                success_types.append(share_type)
            else:
                failed_types.append(share_type)

        conn.close()

        msg_parts = []
        if success_types:
            msg_parts.append(f"成功共享：{', '.join(success_types)}")
        if failed_types:
            msg_parts.append(f"共享失败：{', '.join(failed_types)}")

        await share_multi_config.finish(f"与群 {target_group} 的共享结果：\n" + "\n".join(msg_parts))

    except ValueError:
        await share_multi_config.finish("群号必须是数字")
    except FinishedException:
        # NoneBot2 的正常结束异常，不需要处理
        raise
    except Exception as e:
        logger.error(f"多类型共享失败: {e}")
        await share_multi_config.finish("共享失败，请稍后重试")

@quit_multi_share.handle()
async def handle_quit_multi_share(event: GroupMessageEvent, args: Message = CommandArg()):
    if not is_plugin_enabled(event.group_id):
        await quit_multi_share.finish("群管理功能未启用，请先使用 #群管理 开启")
        return

    args_text = args.extract_plain_text().strip()
    if not args_text:
        await quit_multi_share.finish("请指定要退出的共享类型，格式：#退出多共享 [类型1,类型2...]")
        return

    share_types = [t.strip() for t in args_text.split(',')]
    valid_types = ["配置", "黑名单", "违规词"]
    invalid_types = [t for t in share_types if t not in valid_types]

    if invalid_types:
        await quit_multi_share.finish(f"无效的共享类型：{', '.join(invalid_types)}\n支持的类型：{', '.join(valid_types)}")
        return

    success_types = []
    failed_types = []

    for share_type in share_types:
        if remove_group_share(event.group_id, share_type):
            success_types.append(share_type)
        else:
            failed_types.append(share_type)

    msg_parts = []
    if success_types:
        msg_parts.append(f"成功退出：{', '.join(success_types)}")
    if failed_types:
        msg_parts.append(f"退出失败：{', '.join(failed_types)}（可能未共享）")

    if not msg_parts:
        await quit_multi_share.finish("没有有效的操作")
    else:
        await quit_multi_share.finish("退出共享结果：\n" + "\n".join(msg_parts))

@show_shares.handle()
async def handle_show_shares(event: GroupMessageEvent):
    if not is_plugin_enabled(event.group_id):
        await show_shares.finish("群管理功能未启用，请先使用 #群管理 开启")
        return

    shares = get_group_shares(event.group_id)

    if not shares:
        await show_shares.finish("当前群组没有共享任何配置")
        return

    msg = f"当前群组的共享状态：\n"
    for share_type, share_from in shares.items():
        msg += f"• {share_type}：共享自群 {share_from}\n"

    await show_shares.finish(msg.strip())

# ===== 高级违规词管理处理器 =====

@add_advanced_banned_word.handle()
async def handle_add_advanced_banned_word(event: GroupMessageEvent, args: Message = CommandArg()):
    if not is_plugin_enabled(event.group_id):
        await add_advanced_banned_word.finish("群管理功能未启用，请先使用 #群管理 开启")
        return

    args_text = args.extract_plain_text().strip()
    if not args_text:
        await add_advanced_banned_word.finish("请提供违规词和配置，格式：#添加高级违规词 [违规词] [禁言/踢出] [时长] [阈值]")
        return

    parts = args_text.split()
    if len(parts) < 4:
        await add_advanced_banned_word.finish("参数不足，格式：#添加高级违规词 [违规词] [禁言/踢出] [时长] [阈值]")
        return

    word = parts[0]
    punishment_type_cn = parts[1]

    if punishment_type_cn not in ["禁言", "踢出"]:
        await add_advanced_banned_word.finish("处罚方式只能是：禁言 或 踢出")
        return

    try:
        punishment_duration = int(parts[2])
        violation_threshold = int(parts[3])

        if punishment_duration <= 0 or violation_threshold <= 0:
            await add_advanced_banned_word.finish("时长和阈值必须大于0")
            return
    except ValueError:
        await add_advanced_banned_word.finish("时长和阈值必须是有效数字")
        return

    punishment_type = 'mute' if punishment_type_cn == '禁言' else 'kick'

    success = add_banned_word(
        event.group_id, word, 'recall',
        punishment_type, punishment_duration, violation_threshold,
        event.user_id
    )

    if success:
        duration_text = f" {punishment_duration}分钟" if punishment_type == 'mute' else ""
        await add_advanced_banned_word.finish(
            f"成功添加高级违规词：{word}\n"
            f"处罚方式：{punishment_type_cn}{duration_text}\n"
            f"违规阈值：{violation_threshold}次"
        )
    else:
        await add_advanced_banned_word.finish("添加失败，请稍后重试")

@modify_banned_word.handle()
async def handle_modify_banned_word(event: GroupMessageEvent, args: Message = CommandArg()):
    if not is_plugin_enabled(event.group_id):
        await modify_banned_word.finish("群管理功能未启用，请先使用 #群管理 开启")
        return

    args_text = args.extract_plain_text().strip()
    if not args_text:
        await modify_banned_word.finish("请提供违规词和新配置，格式：#修改违规词 [违规词] [禁言/踢出] [时长] [阈值]")
        return

    parts = args_text.split()
    if len(parts) < 4:
        await modify_banned_word.finish("参数不足，格式：#修改违规词 [违规词] [禁言/踢出] [时长] [阈值]")
        return

    word = parts[0]
    punishment_type_cn = parts[1]

    if punishment_type_cn not in ["禁言", "踢出"]:
        await modify_banned_word.finish("处罚方式只能是：禁言 或 踢出")
        return

    try:
        punishment_duration = int(parts[2])
        violation_threshold = int(parts[3])

        if punishment_duration <= 0 or violation_threshold <= 0:
            await modify_banned_word.finish("时长和阈值必须大于0")
            return
    except ValueError:
        await modify_banned_word.finish("时长和阈值必须是有效数字")
        return

    punishment_type = 'mute' if punishment_type_cn == '禁言' else 'kick'

    # 检查违规词是否存在
    banned_words = get_banned_words(event.group_id)
    word_exists = any(bw[0] == word.lower() for bw in banned_words)

    if not word_exists:
        await modify_banned_word.finish(f"违规词 '{word}' 不存在")
        return

    # 更新违规词配置（通过重新添加实现）
    success = add_banned_word(
        event.group_id, word, 'recall',
        punishment_type, punishment_duration, violation_threshold,
        event.user_id
    )

    if success:
        duration_text = f" {punishment_duration}分钟" if punishment_type == 'mute' else ""
        await modify_banned_word.finish(
            f"成功修改违规词：{word}\n"
            f"新处罚方式：{punishment_type_cn}{duration_text}\n"
            f"新违规阈值：{violation_threshold}次"
        )
    else:
        await modify_banned_word.finish("修改失败，请稍后重试")

@migrate_shares.handle()
async def handle_migrate_shares(event: GroupMessageEvent):
    if not is_plugin_enabled(event.group_id):
        await migrate_shares.finish("群管理功能未启用，请先使用 #群管理 开启")
        return

    # 执行迁移
    success = migrate_legacy_shares(event.group_id)

    if success:
        # 获取迁移后的共享状态
        shares = get_group_shares(event.group_id)

        if shares:
            msg = "✅ 共享配置迁移完成！\n\n当前共享状态：\n"
            for share_type, share_from in shares.items():
                msg += f"• {share_type}：共享自群 {share_from}\n"
            msg += "\n现在可以使用 #共享多配置 命令同时共享多种类型了！"
        else:
            msg = "✅ 迁移完成，但当前群组没有共享配置"

        await migrate_shares.finish(msg)
    else:
        await migrate_shares.finish("❌ 迁移失败，请稍后重试")

@diagnose_speech_monitor.handle()
async def handle_diagnose_speech_monitor(event: GroupMessageEvent):
    if not is_plugin_enabled(event.group_id):
        await diagnose_speech_monitor.finish("群管理功能未启用，请先使用 #群管理 开启")
        return

    # 收集诊断信息
    msg = f"🔍 发言监控诊断报告 - 群 {event.group_id}\n\n"

    # 1. 插件状态
    plugin_enabled = is_plugin_enabled(event.group_id)
    msg += f"1. 插件状态：{'✅ 已启用' if plugin_enabled else '❌ 未启用'}\n"

    # 2. 共享状态
    shares = get_group_shares(event.group_id)
    if shares:
        msg += f"2. 共享状态：\n"
        for share_type, share_from in shares.items():
            msg += f"   • {share_type}：共享自群 {share_from}\n"
    else:
        msg += f"2. 共享状态：❌ 无共享配置\n"

    # 3. 发言监控配置
    config = get_speech_monitor_config(event.group_id)
    effective_config_group = shares.get('配置', event.group_id)
    msg += f"3. 发言监控配置（来源群 {effective_config_group}）：\n"
    msg += f"   • 状态：{'✅ 已启用' if config['enabled'] else '❌ 未启用'}\n"
    msg += f"   • 违规阈值：{config['threshold']} 次\n"
    msg += f"   • 处罚方式：{config['punishment_type']}\n"
    msg += f"   • 处罚时长：{config['punishment_duration']} 分钟\n"

    # 4. 违规词配置
    banned_words = get_banned_words(event.group_id)
    effective_words_group = shares.get('违规词', event.group_id)
    msg += f"4. 违规词配置（来源群 {effective_words_group}）：\n"
    msg += f"   • 违规词数量：{len(banned_words)} 个\n"
    if banned_words:
        words_list = [word_data[0] for word_data in banned_words[:5]]  # 只显示前5个
        msg += f"   • 违规词示例：{', '.join(words_list)}"
        if len(banned_words) > 5:
            msg += f" 等{len(banned_words)}个"
        msg += "\n"

    # 5. 测试消息检测
    test_message = "vpn"
    has_violation, violation_word, action, punishment_type, punishment_duration, violation_threshold = check_message_for_violations(
        event.group_id, test_message
    )
    msg += f"5. 测试检测（消息：'{test_message}'）：\n"
    msg += f"   • 检测结果：{'🚫 违规' if has_violation else '✅ 正常'}\n"
    if has_violation:
        msg += f"   • 违规词：{violation_word}\n"
        msg += f"   • 处罚方式：{punishment_type}\n"
        msg += f"   • 处罚时长：{punishment_duration} 分钟\n"
        msg += f"   • 违规阈值：{violation_threshold} 次\n"

    # 6. 建议
    msg += f"\n💡 建议：\n"
    if not plugin_enabled:
        msg += f"   • 请先执行 #群管理 开启\n"
    if not config['enabled']:
        if '配置' in shares:
            msg += f"   • 请在源群 {shares['配置']} 中执行 #发言监控 开启\n"
        else:
            msg += f"   • 请执行 #发言监控 开启\n"
    if not banned_words:
        if '违规词' in shares:
            msg += f"   • 请在源群 {shares['违规词']} 中添加违规词\n"
        else:
            msg += f"   • 请执行 #添加违规词 [违规词] 或 #添加高级违规词 [违规词] [处罚方式] [时长] [阈值]\n"

    await diagnose_speech_monitor.finish(msg.strip())