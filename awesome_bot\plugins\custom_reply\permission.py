from typing import Union, Set
from nonebot.adapters import Bo<PERSON>, Event
from nonebot.permission import Permission

from .config import config

# 超级用户权限
def is_superuser(event: Event) -> bool:
    """检查是否为超级用户"""
    user_id = getattr(event, "user_id", None)
    if user_id is None:
        return False
    return str(user_id) in config.custom_reply_superusers

SUPERUSER = Permission(is_superuser)

# 检查是否在黑名单中
def is_not_blacklisted(event: Event) -> bool:
    """检查是否不在黑名单中"""
    user_id = getattr(event, "user_id", None)
    if user_id is None:
        return True
    return user_id not in config.custom_reply_blacklist

NOT_BLACKLISTED = Permission(is_not_blacklisted)

# 组合权限：超级用户或非黑名单用户
CUSTOM_REPLY_PERMISSION = SUPERUSER | NOT_BLACKLISTED 