from typing import List, Tuple, Optional, Set
from pathlib import Path
from pydantic import BaseModel, Field
from nonebot import get_driver
from nonebot.config import Config as NBConfig

class PluginConfig(BaseModel):
    """自定义回复插件配置"""
    # 超级管理员QQ号列表
    custom_reply_superusers: List[str] = Field(default_factory=list)
    # 默认启用状态
    custom_reply_default_enabled: bool = False
    # 单条消息最大触发关键词数
    custom_reply_max_trigger: int = 2
    # 全局QQ黑名单
    custom_reply_blacklist: List[int] = Field(default_factory=list)
    # 群聊延迟区间(毫秒)
    custom_reply_group_delay: Tuple[int, int] = (300, 3000)
    # 私聊延迟区间(毫秒)
    custom_reply_private_delay: Tuple[int, int] = (1000, 6000)
    # 图片保存目录
    custom_reply_image_dir: Path = Path("data/custom_reply/images")
    # 数据保存目录
    custom_reply_data_dir: Path = Path("data/custom_reply")

# 全局配置
global_config = get_driver().config
config = PluginConfig.parse_obj(global_config)

# 确保必要的目录存在
config.custom_reply_image_dir.mkdir(parents=True, exist_ok=True)
config.custom_reply_data_dir.mkdir(parents=True, exist_ok=True) 