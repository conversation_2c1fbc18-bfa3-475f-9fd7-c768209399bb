import json
import asyncio
import hashlib
import random
import time
from typing import Dict, List, Set, Optional, Union, Any, Tuple
from datetime import datetime
from pathlib import Path
import aiofiles
from pydantic import BaseModel
import logging

from .config import config

# 初始化随机数生成器
random.seed(int(time.time()))

# 获取日志记录器
logger = logging.getLogger("custom_reply")

class KeywordOption(BaseModel):
    """关键词回复选项模型"""
    content: str
    image_ids: List[str] = []

class KeywordData(BaseModel):
    """关键词数据模型"""
    keyword: str
    mode: str  # "exact" 精确, "fuzzy" 模糊
    options: List[KeywordOption] = []

class TimerData(BaseModel):
    """定时消息数据模型"""
    name: str
    time: str  # 格式: "HH:MM"
    content: str
    image_ids: List[str] = []
    chat_type: str  # "group" 或 "private"
    chat_id: int

class GroupConfig(BaseModel):
    """群组配置模型"""
    enabled: bool = False

class CustomReplyData(BaseModel):
    """插件数据存储模型"""
    keywords: Dict[str, KeywordData] = {}
    timers: Dict[str, TimerData] = {}
    group_configs: Dict[str, GroupConfig] = {}
    backup_group: Optional[int] = None

class DataManager:
    """数据管理器"""
    def __init__(self):
        self.data_file = config.custom_reply_data_dir / "data.json"
        self.image_dir = config.custom_reply_image_dir
        self.data = CustomReplyData()
        self.lock = asyncio.Lock()
        
    async def load_data(self) -> None:
        """加载数据"""
        try:
            if self.data_file.exists():
                async with aiofiles.open(self.data_file, "r", encoding="utf-8") as f:
                    data_json = await f.read()
                    data_dict = json.loads(data_json)
                    
                    # 修复可能存在的HTML实体编码
                    data_dict = self._unescape_variables_in_dict(data_dict)
                    
                    self.data = CustomReplyData.parse_obj(data_dict)
                    
                    # 日志记录已加载的关键词数量和选项总数
                    total_options = sum(len(k.options) for k in self.data.keywords.values())
                    logger.info(f"已加载 {len(self.data.keywords)} 个关键词，共 {total_options} 个回复选项")
                    
                    # 重新初始化随机种子
                    random.seed(int(time.time()))
            else:
                await self.save_data()
        except Exception as e:
            logger.error(f"加载数据时出错: {str(e)}")
            # 创建默认数据
            self.data = CustomReplyData()
            await self.save_data()
    
    async def save_data(self) -> None:
        """保存数据"""
        async with self.lock:
            # 转换为字典，然后使用json模块处理
            data_dict = self.data.dict()
            
            # 修复可能已被转义的变量标记
            data_dict = self._unescape_variables_in_dict(data_dict)
            
            async with aiofiles.open(self.data_file, "w", encoding="utf-8") as f:
                json_str = json.dumps(data_dict, ensure_ascii=False, indent=4)
                await f.write(json_str)
    
    def _unescape_variables_in_dict(self, data_dict: Dict) -> Dict:
        """递归修复字典中的转义变量标记"""
        if isinstance(data_dict, dict):
            result = {}
            for k, v in data_dict.items():
                result[k] = self._unescape_variables_in_dict(v)
            return result
        elif isinstance(data_dict, list):
            return [self._unescape_variables_in_dict(item) for item in data_dict]
        elif isinstance(data_dict, str):
            # 修复HTML实体编码的方括号
            return data_dict.replace("&#91;", "[").replace("&#93;", "]")
        else:
            return data_dict
    
    async def save_image(self, image_data: bytes) -> str:
        """保存图片并返回ID"""
        # 使用图片数据的哈希作为ID
        image_hash = hashlib.md5(image_data).hexdigest()
        image_path = self.image_dir / f"{image_hash}.png"
        
        async with aiofiles.open(image_path, "wb") as f:
            await f.write(image_data)
        
        return image_hash
    
    async def get_image_path(self, image_id: str) -> Optional[Path]:
        """获取图片路径"""
        image_path = self.image_dir / f"{image_id}.png"
        if image_path.exists():
            return image_path
        return None
    
    async def delete_image(self, image_id: str) -> bool:
        """删除图片"""
        image_path = self.image_dir / f"{image_id}.png"
        if image_path.exists():
            image_path.unlink()
            return True
        return False

    async def is_group_enabled(self, group_id: int) -> bool:
        """检查群组是否启用"""
        group_key = str(group_id)
        if group_key in self.data.group_configs:
            return self.data.group_configs[group_key].enabled
        return config.custom_reply_default_enabled
    
    async def set_group_enabled(self, group_id: int, enabled: bool) -> None:
        """设置群组启用状态"""
        group_key = str(group_id)
        if group_key not in self.data.group_configs:
            self.data.group_configs[group_key] = GroupConfig()
        self.data.group_configs[group_key].enabled = enabled
        await self.save_data()
    
    async def add_keyword(self, keyword: str, mode: str, content: str, image_ids: List[str] = []) -> bool:
        """添加关键词"""
        if keyword in self.data.keywords:
            return False
        
        self.data.keywords[keyword] = KeywordData(
            keyword=keyword,
            mode=mode,
            options=[KeywordOption(content=content, image_ids=image_ids)]
        )
        await self.save_data()
        return True
    
    async def update_keyword(self, keyword: str, mode: str, content: str, image_ids: List[str] = []) -> bool:
        """更新关键词"""
        if keyword not in self.data.keywords:
            return False
        
        self.data.keywords[keyword].mode = mode
        self.data.keywords[keyword].options = [KeywordOption(content=content, image_ids=image_ids)]
        await self.save_data()
        return True
    
    async def delete_keyword(self, keyword: str) -> bool:
        """删除关键词"""
        if keyword not in self.data.keywords:
            return False
        
        # 删除关联的图片文件
        for option in self.data.keywords[keyword].options:
            for image_id in option.image_ids:
                await self.delete_image(image_id)
        
        del self.data.keywords[keyword]
        await self.save_data()
        return True
    
    async def add_keyword_option(self, keyword: str, content: str, image_ids: List[str] = []) -> bool:
        """添加关键词选项"""
        if keyword not in self.data.keywords:
            return False
        
        self.data.keywords[keyword].options.append(
            KeywordOption(content=content, image_ids=image_ids)
        )
        await self.save_data()
        return True
    
    async def update_keyword_option(self, keyword: str, index: int, content: str, image_ids: List[str] = []) -> bool:
        """更新关键词选项"""
        if keyword not in self.data.keywords:
            return False
        
        if index < 0 or index >= len(self.data.keywords[keyword].options):
            return False
        
        # 删除旧图片
        for image_id in self.data.keywords[keyword].options[index].image_ids:
            if image_id not in image_ids:
                await self.delete_image(image_id)
        
        self.data.keywords[keyword].options[index] = KeywordOption(content=content, image_ids=image_ids)
        await self.save_data()
        return True
    
    async def delete_keyword_option(self, keyword: str, index: int) -> bool:
        """删除关键词选项"""
        if keyword not in self.data.keywords:
            return False
        
        if index < 0 or index >= len(self.data.keywords[keyword].options):
            return False
        
        # 如果是最后一个选项，则删除整个关键词
        if len(self.data.keywords[keyword].options) == 1:
            return await self.delete_keyword(keyword)
        
        # 删除图片
        for image_id in self.data.keywords[keyword].options[index].image_ids:
            await self.delete_image(image_id)
        
        self.data.keywords[keyword].options.pop(index)
        await self.save_data()
        return True
    
    async def rename_keyword(self, old_keyword: str, new_keyword: str) -> bool:
        """重命名关键词"""
        if old_keyword not in self.data.keywords or new_keyword in self.data.keywords:
            return False
        
        keyword_data = self.data.keywords.pop(old_keyword)
        keyword_data.keyword = new_keyword
        self.data.keywords[new_keyword] = keyword_data
        await self.save_data()
        return True
    
    async def change_keyword_mode(self, keyword: str, mode: str) -> bool:
        """修改关键词匹配模式"""
        if keyword not in self.data.keywords:
            return False
        
        self.data.keywords[keyword].mode = mode
        await self.save_data()
        return True
    
    async def get_keyword_data(self, keyword: str) -> Optional[KeywordData]:
        """获取关键词数据"""
        return self.data.keywords.get(keyword)
    
    async def add_timer(self, name: str, time: str, content: str, 
                       image_ids: List[str], chat_type: str, chat_id: int) -> bool:
        """添加定时消息"""
        timer_key = f"{chat_type}_{chat_id}_{name}"
        if timer_key in self.data.timers:
            return False
        
        self.data.timers[timer_key] = TimerData(
            name=name,
            time=time,
            content=content,
            image_ids=image_ids,
            chat_type=chat_type,
            chat_id=chat_id
        )
        await self.save_data()
        return True
    
    async def delete_timer(self, chat_type: str, chat_id: int, name: str) -> bool:
        """删除定时消息"""
        timer_key = f"{chat_type}_{chat_id}_{name}"
        if timer_key not in self.data.timers:
            return False
        
        # 删除图片
        for image_id in self.data.timers[timer_key].image_ids:
            await self.delete_image(image_id)
        
        del self.data.timers[timer_key]
        await self.save_data()
        return True
    
    async def get_all_timers(self) -> Dict[str, TimerData]:
        """获取所有定时消息"""
        return self.data.timers
    
    async def set_backup_group(self, group_id: int) -> None:
        """设置备份群"""
        self.data.backup_group = group_id
        await self.save_data()
    
    async def get_backup_group(self) -> Optional[int]:
        """获取备份群"""
        return self.data.backup_group
    
    async def get_random_response(self, keyword: str) -> Optional[Tuple[str, List[str]]]:
        """获取随机回复"""
        if keyword not in self.data.keywords:
            logger.warning(f"获取随机回复失败：关键词 {keyword} 不存在")
            return None
        
        keyword_data = self.data.keywords[keyword]
        if not keyword_data.options:
            logger.warning(f"获取随机回复失败：关键词 {keyword} 没有回复选项")
            return None
        
        # 记录选项数量
        option_count = len(keyword_data.options)
        logger.info(f"关键词 {keyword} 有 {option_count} 个选项")
        
        # 确保使用随机选择
        # 为了增加随机性，每次获取随机回复时都重新初始化随机种子
        random.seed(int(time.time() * 1000) % (2**32))
        
        # 选择一个随机选项
        option_index = random.randint(0, option_count - 1)
        option = keyword_data.options[option_index]
        
        logger.info(f"随机选择第 {option_index + 1} 个选项: {option.content[:20]}..." if len(option.content) > 20 else option.content)
        
        return option.content, option.image_ids

    async def get_keyword_statistics(self) -> Dict[str, int]:
        """获取关键词统计"""
        stats = {
            "总关键词数": len(self.data.keywords),
            "精确匹配": sum(1 for k in self.data.keywords.values() if k.mode == "exact"),
            "模糊匹配": sum(1 for k in self.data.keywords.values() if k.mode == "fuzzy"),
            "总选项数": sum(len(k.options) for k in self.data.keywords.values()),
            "定时消息数": len(self.data.timers)
        }
        return stats
    
    async def copy_keyword(self, source_keyword: str, target_keyword: str) -> bool:
        """复制关键词"""
        if source_keyword not in self.data.keywords or target_keyword in self.data.keywords:
            return False
        
        source_data = self.data.keywords[source_keyword]
        
        # 复制图片
        new_options = []
        for option in source_data.options:
            new_image_ids = []
            for image_id in option.image_ids:
                image_path = await self.get_image_path(image_id)
                if image_path:
                    async with aiofiles.open(image_path, "rb") as f:
                        image_data = await f.read()
                        new_image_id = await self.save_image(image_data)
                        new_image_ids.append(new_image_id)
            
            new_options.append(KeywordOption(
                content=option.content,
                image_ids=new_image_ids
            ))
        
        self.data.keywords[target_keyword] = KeywordData(
            keyword=target_keyword,
            mode=source_data.mode,
            options=new_options
        )
        
        await self.save_data()
        return True

    async def fix_escaped_variables(self) -> int:
        """修复已经被HTML实体编码的变量
        
        Returns:
            修复的条目数量
        """
        fixed_count = 0
        
        # 修复关键词内容
        for keyword, keyword_data in self.data.keywords.items():
            for i, option in enumerate(keyword_data.options):
                original_content = option.content
                fixed_content = self._unescape_variables_in_dict(original_content)
                
                if original_content != fixed_content:
                    self.data.keywords[keyword].options[i].content = fixed_content
                    fixed_count += 1
        
        # 修复定时消息内容
        for timer_key, timer_data in self.data.timers.items():
            original_content = timer_data.content
            fixed_content = self._unescape_variables_in_dict(original_content)
            
            if original_content != fixed_content:
                self.data.timers[timer_key].content = fixed_content
                fixed_count += 1
        
        # 保存修复后的数据
        if fixed_count > 0:
            await self.save_data()
            logger.info(f"已修复 {fixed_count} 条被错误编码的变量")
        
        return fixed_count

# 创建数据管理器实例
data_manager = DataManager() 