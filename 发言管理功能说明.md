# 发言管理功能说明 v2.0

## 功能概述

发言管理功能是群管插件的增强模块，用于自动检测和处理群聊中的违规内容。该功能支持：

- ✅ **智能违规词检测** - 不分大小写的包含检测
- ✅ **自动消息撤回** - 检测到违规内容立即撤回
- ✅ **违规次数统计** - 自动记录用户违规行为
- ✅ **独立处罚配置** - 每个违规词可独立设置处罚方式
- ✅ **多类型共享机制** - 支持同时共享多种配置类型
- ✅ **灵活配置管理** - 支持多种参数自定义
- ✅ **白名单机制** - 管理员和群主免检

## 🆕 v2.0 新特性

### 1. 违规词独立配置
- 每个违规词可单独设置处罚方式（禁言/踢出）
- 每个违规词可单独设置处罚时长
- 每个违规词可单独设置违规阈值
- 优先使用违规词独立配置，无配置时使用全局配置

### 2. 多类型同时共享
- 支持同时共享：配置、黑名单、违规词
- 不再受限于只能共享一种类型
- 可以灵活组合不同的共享类型

## 数据库设计

### 新增数据表

#### 1. banned_words (违规词表) - 已升级
```sql
CREATE TABLE banned_words (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    group_id INTEGER,                    -- 群号
    word TEXT NOT NULL,                  -- 违规词（存储为小写）
    action TEXT DEFAULT 'recall',       -- 处理动作
    punishment_type TEXT DEFAULT 'mute', -- 处罚类型（mute/kick）
    punishment_duration INTEGER DEFAULT 60, -- 处罚时长（分钟）
    violation_threshold INTEGER DEFAULT 3,  -- 违规阈值
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER,                  -- 创建者QQ号
    UNIQUE(group_id, word)
);
```

#### 4. group_shares (多类型共享表) - 新增
```sql
CREATE TABLE group_shares (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    group_id INTEGER,                    -- 群号
    share_type TEXT,                     -- 共享类型（配置/黑名单/违规词）
    share_from INTEGER,                  -- 共享源群号
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(group_id, share_type)
);
```

#### 2. violation_records (违规记录表)
```sql
CREATE TABLE violation_records (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    group_id INTEGER,                    -- 群号
    user_id INTEGER,                     -- 违规用户QQ号
    message_id INTEGER,                  -- 消息ID
    violation_word TEXT,                 -- 触发的违规词
    original_message TEXT,               -- 原始消息内容
    action_taken TEXT,                   -- 执行的动作
    violation_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 3. violation_counts (违规计数表)
```sql
CREATE TABLE violation_counts (
    group_id INTEGER,                    -- 群号
    user_id INTEGER,                     -- 用户QQ号
    violation_count INTEGER DEFAULT 0,   -- 违规次数
    last_violation_time TIMESTAMP,      -- 最后违规时间
    PRIMARY KEY (group_id, user_id)
);
```

### 配置字段扩展

在 `group_config` 表中新增以下字段：

- `speech_monitor_enabled` - 发言监控开关 (0/1)
- `violation_threshold` - 违规阈值 (默认3次)
- `punishment_type` - 处罚类型 ('mute'/'kick')
- `punishment_duration` - 处罚时长 (分钟，默认60)

## 命令使用说明

### 基础管理命令

#### 1. 开启/关闭发言监控
```
#发言监控 开启
#发言监控 关闭
```

#### 2. 添加违规词
```
#添加违规词 违规内容                    # 使用全局配置
#添加高级违规词 违规词 禁言 30 5         # 独立配置：禁言30分钟，5次阈值
#添加高级违规词 违规词 踢出 0 3          # 独立配置：直接踢出，3次阈值
```

支持批量添加（换行分隔）：
```
#添加违规词
违规词1
违规词2
违规词3
```

#### 3. 移除违规词
```
#移除违规词 违规内容
```

同样支持批量移除：
```
#移除违规词
违规词1
违规词2
```

#### 4. 查看违规词列表
```
#违规词列表
```

### 配置管理命令

#### 5. 设置违规阈值
```
#设置违规阈值 5
```

#### 6. 设置处罚方式
```
#设置处罚方式 禁言 30    # 禁言30分钟
#设置处罚方式 踢出       # 直接踢出
```

#### 7. 查看发言监控配置
```
#发言监控配置
```

### 记录查询命令

#### 8. 查看违规记录
```
#查看违规记录              # 查看所有用户违规统计
#查看违规记录 123456789    # 查看指定用户违规详情
```

#### 9. 重置违规记录
```
#重置违规记录 123456789
```

### 高级管理命令

#### 10. 修改违规词配置
```
#修改违规词 违规词 禁言 60 5           # 修改为禁言60分钟，5次阈值
#修改违规词 违规词 踢出 0 2            # 修改为直接踢出，2次阈值
```

### 多类型共享命令

#### 11. 同时共享多种配置
```
#共享多配置 123456789 配置,黑名单       # 同时共享配置和黑名单
#共享多配置 123456789 配置,黑名单,违规词 # 同时共享三种类型
```

#### 12. 退出多种共享
```
#退出多共享 配置,黑名单                # 同时退出配置和黑名单共享
#退出多共享 违规词                    # 退出违规词共享
```

#### 13. 查看共享状态
```
#查看共享状态                         # 查看当前群的所有共享配置
```

## 工作流程

### 1. 消息检测流程

```mermaid
graph TD
    A[用户发送消息] --> B{是否为命令消息?}
    B -->|是| C[跳过检测]
    B -->|否| D{用户是否为管理员?}
    D -->|是| C
    D -->|否| E{发言监控是否开启?}
    E -->|否| C
    E -->|是| F[检查消息是否包含违规词]
    F -->|否| C
    F -->|是| G[撤回消息]
    G --> H[记录违规行为]
    H --> I[更新违规计数]
    I --> J{是否达到处罚阈值?}
    J -->|否| K[发送警告消息]
    J -->|是| L[执行处罚]
    L --> M[发送处罚通知]
    K --> N[结束]
    M --> N
    C --> N
```

### 2. 违规词检测逻辑

- **不分大小写**: 所有违规词存储为小写，检测时将消息转为小写比较
- **包含检测**: 使用 `in` 操作符进行子字符串匹配
- **优先匹配**: 按违规词列表顺序检测，匹配到第一个即停止

### 3. 处罚机制

- **违规计数**: 每次违规自动 +1
- **阈值判断**: 达到设定阈值触发处罚
- **处罚类型**:
  - `mute`: 禁言指定时长（分钟）
  - `kick`: 直接踢出群聊

## 技术特性

### 1. 性能优化

- **规则优先级**: 发言监控设置为较低优先级(15)，不阻塞其他功能
- **快速跳过**: 命令消息和管理员消息直接跳过检测
- **数据库索引**: 关键查询字段建立索引提升性能

### 2. 安全机制

- **权限控制**: 只有管理员可以配置违规词和参数
- **白名单保护**: 管理员和群主不受发言监控限制
- **错误处理**: 完善的异常处理，避免功能崩溃

### 3. 扩展性设计

- **模块化结构**: 发言管理功能独立封装，便于维护
- **配置灵活**: 支持群组独立配置，互不影响
- **数据完整**: 详细记录违规行为，便于后续分析

## 使用示例

### 场景1: 基础违规词过滤

```bash
# 1. 开启发言监控
#发言监控 开启

# 2. 添加违规词
#添加违规词 广告
#添加违规词 spam

# 3. 设置处罚参数
#设置违规阈值 3
#设置处罚方式 禁言 10

# 4. 查看配置
#发言监控配置
```

### 场景2: 批量管理违规词

```bash
# 批量添加
#添加违规词
广告
推广
spam
垃圾信息
刷屏

# 查看列表
#违规词列表

# 批量移除
#移除违规词
spam
垃圾信息
```

### 场景3: 违规记录管理

```bash
# 查看群组违规统计
#查看违规记录

# 查看特定用户详情
#查看违规记录 123456789

# 重置用户违规次数
#重置违规记录 123456789
```

## 注意事项

1. **权限要求**: 机器人需要有撤回消息和管理群成员的权限
2. **检测范围**: 只检测普通群消息，不检测命令消息
3. **管理员免检**: 群管理员和群主发送的消息不会被检测
4. **大小写不敏感**: 违规词检测不区分大小写
5. **包含匹配**: 只要消息包含违规词就会触发，不是完全匹配
6. **处罚累积**: 违规次数会持续累积，直到手动重置

## 故障排除

### 常见问题

1. **消息无法撤回**
   - 检查机器人是否有撤回消息权限
   - 确认机器人不是普通成员

2. **无法执行处罚**
   - 检查机器人是否有管理群成员权限
   - 确认目标用户不是管理员

3. **违规词不生效**
   - 确认发言监控功能已开启
   - 检查违规词是否正确添加
   - 验证群管理功能是否启用

### 调试方法

1. 查看日志输出确认功能运行状态
2. 使用 `#发言监控配置` 检查当前设置
3. 使用 `#违规词列表` 确认违规词配置
4. 运行测试脚本验证数据库操作

---

**版本**: v1.0  
**更新时间**: 2024-12-19  
**兼容性**: NoneBot2 + OneBot V11
