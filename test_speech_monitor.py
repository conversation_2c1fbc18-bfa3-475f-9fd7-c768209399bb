#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
发言管理功能测试脚本
测试违规词检测、数据库操作等核心功能
"""

import sys
import os
import sqlite3
from pathlib import Path

# 创建数据目录
data_dir = Path(__file__).parent / "awesome_bot" / "plugins" / "group_manager" / "data"
data_dir.mkdir(parents=True, exist_ok=True)

# 直接实现测试所需的函数（简化版本）
def init_test_db():
    """初始化测试数据库"""
    db_path = data_dir / "group_manager.db"
    conn = sqlite3.connect(str(db_path))
    cursor = conn.cursor()

    # 创建违规词管理表
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS banned_words (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        group_id INTEGER,
        word TEXT NOT NULL,
        action TEXT DEFAULT 'recall',
        created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        created_by INTEGER,
        UNIQUE(group_id, word)
    )
    ''')

    # 创建违规记录表
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS violation_records (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        group_id INTEGER,
        user_id INTEGER,
        message_id INTEGER,
        violation_word TEXT,
        original_message TEXT,
        action_taken TEXT,
        violation_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
    ''')

    # 创建违规计数表
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS violation_counts (
        group_id INTEGER,
        user_id INTEGER,
        violation_count INTEGER DEFAULT 0,
        last_violation_time TIMESTAMP,
        PRIMARY KEY (group_id, user_id)
    )
    ''')

    # 创建配置表
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS group_config (
        group_id INTEGER PRIMARY KEY,
        speech_monitor_enabled INTEGER DEFAULT 0,
        violation_threshold INTEGER DEFAULT 3,
        punishment_type TEXT DEFAULT 'mute',
        punishment_duration INTEGER DEFAULT 60
    )
    ''')

    conn.commit()
    conn.close()

def add_banned_word(group_id: int, word: str, action: str = 'recall', created_by: int = None) -> bool:
    """添加违规词"""
    db_path = data_dir / "group_manager.db"
    conn = sqlite3.connect(str(db_path))
    cursor = conn.cursor()

    try:
        cursor.execute(
            "INSERT OR REPLACE INTO banned_words (group_id, word, action, created_by) VALUES (?, ?, ?, ?)",
            (group_id, word.lower(), action, created_by)
        )
        conn.commit()
        return True
    except sqlite3.Error as e:
        print(f"添加违规词失败: {e}")
        return False
    finally:
        conn.close()

def remove_banned_word(group_id: int, word: str) -> bool:
    """移除违规词"""
    db_path = data_dir / "group_manager.db"
    conn = sqlite3.connect(str(db_path))
    cursor = conn.cursor()

    try:
        cursor.execute(
            "DELETE FROM banned_words WHERE group_id = ? AND word = ?",
            (group_id, word.lower())
        )
        success = cursor.rowcount > 0
        conn.commit()
        return success
    except sqlite3.Error as e:
        print(f"移除违规词失败: {e}")
        return False
    finally:
        conn.close()

def get_banned_words(group_id: int) -> list:
    """获取群组违规词列表"""
    db_path = data_dir / "group_manager.db"
    conn = sqlite3.connect(str(db_path))
    cursor = conn.cursor()

    try:
        cursor.execute(
            "SELECT word, action FROM banned_words WHERE group_id = ? ORDER BY word",
            (group_id,)
        )
        return cursor.fetchall()
    except sqlite3.Error as e:
        print(f"获取违规词列表失败: {e}")
        return []
    finally:
        conn.close()

def check_message_for_violations(group_id: int, message: str) -> tuple:
    """检查消息是否包含违规词"""
    banned_words = get_banned_words(group_id)
    message_lower = message.lower()

    for word, action in banned_words:
        if word in message_lower:
            return True, word, action

    return False, None, None

print("✅ 测试环境初始化完成")

def test_database_operations():
    """测试数据库操作"""
    print("\n=== 测试数据库操作 ===")
    
    # 初始化数据库
    init_db()
    print("✅ 数据库初始化完成")
    
    test_group_id = 123456789
    test_user_id = 987654321
    
    # 测试添加违规词
    print("\n--- 测试添加违规词 ---")
    test_words = ["测试词1", "TEST", "违规内容"]
    
    for word in test_words:
        result = add_banned_word(test_group_id, word, 'recall', test_user_id)
        print(f"添加违规词 '{word}': {'✅ 成功' if result else '❌ 失败'}")
    
    # 测试获取违规词列表
    print("\n--- 测试获取违规词列表 ---")
    banned_words = get_banned_words(test_group_id)
    print(f"获取到 {len(banned_words)} 个违规词:")
    for word, action in banned_words:
        print(f"  - {word} (动作: {action})")
    
    # 测试违规检测
    print("\n--- 测试违规检测 ---")
    test_messages = [
        "这是正常消息",
        "这里包含测试词1",
        "TEST message here",
        "包含违规内容的消息",
        "NORMAL MESSAGE"
    ]
    
    for msg in test_messages:
        has_violation, violation_word, action = check_message_for_violations(test_group_id, msg)
        status = "🚫 违规" if has_violation else "✅ 正常"
        violation_info = f" (违规词: {violation_word})" if has_violation else ""
        print(f"  '{msg}' -> {status}{violation_info}")
    
    # 测试违规记录
    print("\n--- 测试违规记录 ---")
    record_violation(test_group_id, test_user_id, 12345, "测试词1", "这里包含测试词1", "recall")
    record_violation(test_group_id, test_user_id, 12346, "违规内容", "包含违规内容的消息", "recall")
    print("✅ 违规记录添加完成")
    
    # 测试获取违规次数
    violation_count = get_user_violation_count(test_group_id, test_user_id)
    print(f"用户 {test_user_id} 违规次数: {violation_count}")
    
    # 测试配置管理
    print("\n--- 测试配置管理 ---")
    
    # 更新配置
    config_updates = {
        'speech_monitor_enabled': 1,
        'violation_threshold': 5,
        'punishment_type': 'mute',
        'punishment_duration': 30
    }
    
    result = update_speech_monitor_config(test_group_id, **config_updates)
    print(f"更新配置: {'✅ 成功' if result else '❌ 失败'}")
    
    # 获取配置
    config = get_speech_monitor_config(test_group_id)
    print("当前配置:")
    for key, value in config.items():
        print(f"  {key}: {value}")
    
    # 测试重置违规记录
    print("\n--- 测试重置违规记录 ---")
    reset_result = reset_user_violations(test_group_id, test_user_id)
    print(f"重置违规记录: {'✅ 成功' if reset_result else '❌ 失败'}")
    
    new_count = get_user_violation_count(test_group_id, test_user_id)
    print(f"重置后违规次数: {new_count}")
    
    # 测试移除违规词
    print("\n--- 测试移除违规词 ---")
    for word in test_words:
        result = remove_banned_word(test_group_id, word)
        print(f"移除违规词 '{word}': {'✅ 成功' if result else '❌ 失败'}")
    
    # 验证移除结果
    remaining_words = get_banned_words(test_group_id)
    print(f"移除后剩余违规词数量: {len(remaining_words)}")

def test_case_insensitive_detection():
    """测试不分大小写检测"""
    print("\n=== 测试不分大小写检测 ===")
    
    test_group_id = 123456789
    
    # 添加测试违规词
    add_banned_word(test_group_id, "BadWord", 'recall')
    add_banned_word(test_group_id, "禁止词", 'recall')
    
    # 测试各种大小写组合
    test_cases = [
        ("badword", True),      # 全小写
        ("BADWORD", True),      # 全大写
        ("BadWord", True),      # 原样
        ("bAdWoRd", True),      # 混合大小写
        ("包含badword的消息", True),  # 包含检测
        ("禁止词", True),        # 中文
        ("包含禁止词的内容", True),   # 中文包含
        ("正常内容", False),      # 正常内容
        ("good message", False)  # 正常英文
    ]
    
    print("测试不分大小写检测:")
    for message, expected in test_cases:
        has_violation, violation_word, _ = check_message_for_violations(test_group_id, message)
        result = "✅" if has_violation == expected else "❌"
        status = "违规" if has_violation else "正常"
        violation_info = f" (检测到: {violation_word})" if has_violation else ""
        print(f"  {result} '{message}' -> {status}{violation_info}")
    
    # 清理测试数据
    remove_banned_word(test_group_id, "BadWord")
    remove_banned_word(test_group_id, "禁止词")

def main():
    """主测试函数"""
    print("🚀 开始测试发言管理功能")
    
    try:
        test_database_operations()
        test_case_insensitive_detection()
        print("\n🎉 所有测试完成！")
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
