import random
import asyncio
import re
import time
import aiofiles
from typing import Dict, List, Set, Tuple, Optional, Union
from nonebot import on_command, on_message, get_driver
from nonebot.plugin import PluginMetadata
from nonebot.rule import to_me
from nonebot.adapters import Bot, Event, Message
from nonebot.adapters.onebot.v11 import (
    GroupMessageEvent, 
    PrivateMessageEvent,
    MessageSegment
)
from nonebot.permission import SUPERUSER
from nonebot.params import CommandArg, Command, RawCommand
from nonebot.typing import T_State
import logging

from .config import config
from .data_manager import data_manager
from .permission import CUSTOM_REPLY_PERMISSION, NOT_BLACKLISTED
from .variable_parser import variable_parser
from .timer_manager import timer_manager
from .message_builder import build_message, extract_images_from_message, extract_faces_from_message

__plugin_meta__ = PluginMetadata(
    name="自定义回复",
    description="支持图片、表情和多媒体内容的自定义关键词回复插件",
    usage="查看帮助：#自定义 帮助",
    type="application",
    homepage="https://github.com/username/nonebot-plugin-custom-reply",
    config=config,
    supported_adapters={"~onebot.v11", "~onebot.v12"}
)

# 帮助命令
custom_help = on_command("自定义", aliases={"自定义帮助"}, priority=5, permission=CUSTOM_REPLY_PERMISSION)

@custom_help.handle()
async def handle_help(bot: Bot, event: Event, args: Message = CommandArg()):
    arg_text = args.extract_plain_text().strip()
    
    if arg_text == "帮助":
        # 显示详细帮助
        help_text = (
            "=== 自定义回复插件帮助 ===\n"
            "关键词管理:\n"
            "  #加词 <词> <?模式> <内容> - 添加关键词, 模式可选值: 精确、模糊\n"
            "  #改词 <词> <?模式> <内容> - 修改关键词\n"
            "  #删词 <词> - 删除关键词\n"
            "  #查词 <词> - 查询关键词\n"
            "  #复制词 <原词> <新词> - 复制关键词\n"
            "  #改词名 <原词> <新词> - 修改关键词名称\n"
            "选项管理:\n"
            "  #加选项 <词> <内容> - 添加回复选项\n"
            "  #改选项 <词> <序号> <内容> - 修改回复选项\n"
            "  #删选项 <词> <序号> - 删除回复选项\n"
            "  #改模式 <词> <模式> - 修改匹配模式\n"
            "定时消息:\n"
            "  #加定时 <名称> <时间> <内容> - 添加定时消息\n"
            "  #删定时 <名称> - 删除定时消息\n"
            "查看列表:\n"
            "  #词列表 <?页码> - 查看关键词列表\n"
            "  #定时列表 <?页码> - 查看定时消息列表\n"
            "功能控制:\n"
            "  #开自定义 - 开启自定义回复功能\n"
            "  #关自定义 - 关闭自定义回复功能\n"
            "  #自定义统计 - 查看统计数据\n"
            "  #备份自定义 - 备份配置文件\n"
            "  #重载自定义 - 重新加载配置\n"
            "  #设置备份群 - 设置当前群为备份群\n"
            "  #修复变量 - 修复被错误编码的变量标记\n"
            "变量系统:\n"
            "  #变量 - 查看所有可用变量分类\n"
            "  #变量 <分类> - 查看指定分类的变量\n"
            "\n支持添加图片，发送消息时包含图片即可自动识别"
        )
        await custom_help.finish(help_text)
    elif arg_text == "变量":
        # 显示变量帮助
        var_help_text = (
            "=== 自定义回复插件变量说明 ===\n"
            "在回复内容中使用 [变量名]参数[/变量名] 格式可以实现动态内容\n\n"
            "◆ 基础文本变量\n"
            "  [br] - 换行\n"
            "  [space] - 空格\n"
            "  [n]1-100[/n] - 1-100之间的随机数\n"
            "  [in] - 获取触发词后的文本(转义)\n"
            "  [IN] - 获取触发词后的文本(不转义)\n"
            "  [t]格式[/t] - 当前时间，如[t]YYYY-MM-DD HH:mm:ss[/t]\n"
            "  [rf] - 随机QQ表情\n"
            "  [rt] - 随机一言\n"
            "  [rc] - 撤回当前消息\n\n"
            "◆ 群组相关变量\n"
            "  [gid] - 获取当前群号\n"
            "  [gnick] - 获取发送者群名片\n"
            "  [title] - 获取发送者群头衔\n"
            "  [level] - 获取发送者群等级\n"
            "  [memcount] - 获取当前群成员数\n"
            "  [radmin] - 随机选择一位管理员\n"
            "  [owner] - 获取群主QQ号\n"
            "  [gmc]QQ号[/gmc] - 获取指定QQ的群名片\n"
            "  [jointime]QQ号[/jointime] - 获取入群时间\n"
            "  [lastmsg]QQ号[/lastmsg] - 获取最后发言时间\n\n"
            "◆ 用户交互变量\n"
            "  [bqq] - 机器人QQ号\n"
            "  [sqq] - 发送者QQ号\n"
            "  [tqq] - 被@的人QQ号\n"
            "  [at]QQ号[/at] - @指定QQ\n"
            "  [all] - @全体成员\n"
            "  [rpl] - 引用回复\n"
            "  [pk]QQ号[/pk] - 戳一戳指定QQ\n"
            "  [b]QQ号-分钟[/b] - 禁言指定QQ指定分钟\n"
            "  [av]QQ号[/av] - 获取指定QQ的头像\n"
            "  [rgu]n-m[/rgu] - 随机抽取n-m个群友\n\n"
            "◆ 多媒体变量\n"
            "  [image]URL[/image] - 发送图片\n"
            "  [xml]内容[/xml] - 发送XML消息\n"
            "  [json]内容[/json] - 发送JSON消息\n"
            "  [music]ID[/music] - 发送QQ音乐\n"
            "  [record]URL[/record] - 发送语音\n"
            "  [video]URL[/video] - 发送视频\n\n"
            "◆ 游戏互动变量\n"
            "  [di]点数[/di] - 投掷指定点数骰子\n"
            "  [rps]类型[/rps] - 出拳(1石头2剪刀3布)\n"
            "  [rn]选项1,选项2[/rn] - 随机选择一个选项\n\n"
            "◆ 网络请求变量\n"
            "  [g]URL[/g] - 发送GET请求\n"
            "  [p]URL|数据[/p] - 发送POST请求\n"
            "  [url]URL[/url] - 发送URL请求\n\n"
            "◆ 条件与文件变量\n"
            "  [if]条件[/if] - 条件判断\n"
            "  [path]路径[/path] - 获取文件路径\n"
            "  [file]路径[/file] - 读取文件内容\n\n"
            "◆ 文本格式变量\n"
            "  [code]语言,代码[/code] - 代码格式\n"
            "  [b]内容[/b] - 粗体\n"
            "  [i]内容[/i] - 斜体\n"
            "  [u]内容[/u] - 下划线\n"
            "  [s]内容[/s] - 删除线\n\n"
            "📝 变量可以嵌套使用，例如：\n"
            "  [at][sqq][/at] 你好，[t]HH:mm[/t]好！\n\n"
            "想了解更多详细信息，请使用命令：\n"
            "#变量 - 查看所有变量分类\n"
            "#变量 <分类> - 如 #变量 基础"
        )
        await custom_help.finish(var_help_text)
    else:
        # 显示命令列表
        help_text = (
            "=== 自定义回复插件 ===\n"
            "命令列表:\n"
            "  #加词 #改词 #删词 #查词\n"
            "  #复制词 #改词名 #加选项 #改选项\n"
            "  #删选项 #改模式 #加定时 #删定时\n"
            "  #词列表 #定时列表 #开自定义 #关自定义\n"
            "  #自定义统计 #备份自定义 #重载自定义 #设置备份群\n"
            "  #变量 #测试匹配 #测试随机回复 #调试随机 #修复变量\n"
            "\n输入 #自定义 帮助 查看详细用法\n"
            "输入 #自定义 变量 查看变量说明简介\n"
            "输入 #变量 查看完整变量分类和用法"
        )
        await custom_help.finish(help_text)

# 变量帮助命令
variables_help = on_command("变量", aliases={"变量帮助"}, priority=5, permission=CUSTOM_REPLY_PERMISSION)

@variables_help.handle()
async def handle_variables_help(bot: Bot, event: Event, args: Message = CommandArg()):
    arg_text = args.extract_plain_text().strip()
    
    # 定义变量分类和内容
    variable_categories = {
        "基础": {
            "title": "基础文本变量",
            "description": "用于处理基本文本格式和内容",
            "variables": [
                {"name": "br", "params": "", "desc": "换行符，相当于\\n"},
                {"name": "space", "params": "", "desc": "插入空格"},
                {"name": "n", "params": "范围", "desc": "生成指定范围随机数，如：[n]1-100[/n]"},
                {"name": "in", "params": "", "desc": "获取触发词后的文本(转义处理)"},
                {"name": "IN", "params": "", "desc": "获取触发词后的文本(不转义处理)"},
                {"name": "t", "params": "格式", "desc": "获取当前时间，如：[t]YYYY-MM-DD HH:mm:ss[/t]"},
                {"name": "rf", "params": "", "desc": "随机QQ表情"},
                {"name": "rt", "params": "", "desc": "随机一言"},
                {"name": "rc", "params": "秒数", "desc": "撤回当前消息，可指定延迟秒数"}
            ]
        },
        "群组": {
            "title": "群组相关变量",
            "description": "用于获取群相关信息",
            "variables": [
                {"name": "gid", "params": "", "desc": "获取当前群号"},
                {"name": "gnick", "params": "", "desc": "获取发送者群名片"},
                {"name": "title", "params": "", "desc": "获取发送者群头衔"},
                {"name": "level", "params": "", "desc": "获取发送者群等级"},
                {"name": "memcount", "params": "", "desc": "获取当前群成员数"},
                {"name": "radmin", "params": "", "desc": "随机选择一位管理员"},
                {"name": "owner", "params": "", "desc": "获取群主QQ号"},
                {"name": "gmc", "params": "QQ号", "desc": "获取指定QQ的群名片"},
                {"name": "jointime", "params": "QQ号", "desc": "获取入群时间"},
                {"name": "lastmsg", "params": "QQ号", "desc": "获取最后发言时间"}
            ]
        },
        "用户": {
            "title": "用户交互变量",
            "description": "用于用户交互和操作",
            "variables": [
                {"name": "bqq", "params": "", "desc": "机器人QQ号"},
                {"name": "sqq", "params": "", "desc": "发送者QQ号"},
                {"name": "tqq", "params": "", "desc": "被@的人QQ号"},
                {"name": "at", "params": "QQ号", "desc": "@指定QQ"},
                {"name": "all", "params": "", "desc": "@全体成员"},
                {"name": "rpl", "params": "", "desc": "引用回复"},
                {"name": "pk", "params": "QQ号", "desc": "戳一戳指定QQ"},
                {"name": "b", "params": "QQ号-分钟", "desc": "禁言指定QQ指定分钟"},
                {"name": "av", "params": "QQ号", "desc": "获取指定QQ的头像"},
                {"name": "rgu", "params": "n-m", "desc": "随机抽取n-m个群友"}
            ]
        },
        "多媒体": {
            "title": "多媒体变量",
            "description": "用于发送多媒体内容",
            "variables": [
                {"name": "image", "params": "URL", "desc": "发送图片"},
                {"name": "xml", "params": "内容", "desc": "发送XML消息"},
                {"name": "json", "params": "内容", "desc": "发送JSON消息"},
                {"name": "music", "params": "ID", "desc": "发送QQ音乐"},
                {"name": "record", "params": "URL", "desc": "发送语音"},
                {"name": "video", "params": "URL", "desc": "发送视频"}
            ]
        },
        "游戏": {
            "title": "游戏互动变量",
            "description": "用于游戏和互动功能",
            "variables": [
                {"name": "di", "params": "点数", "desc": "投掷指定点数骰子，不填则随机"},
                {"name": "rps", "params": "类型", "desc": "出拳(1石头2剪刀3布)，不填则随机"},
                {"name": "rn", "params": "选项1,选项2...", "desc": "随机选择一个选项"}
            ]
        },
        "网络": {
            "title": "网络请求变量",
            "description": "用于发送网络请求",
            "variables": [
                {"name": "g", "params": "URL", "desc": "发送GET请求"},
                {"name": "p", "params": "URL|数据", "desc": "发送POST请求"},
                {"name": "url", "params": "URL", "desc": "发送URL请求"}
            ]
        },
        "条件": {
            "title": "条件与文件变量",
            "description": "用于条件判断和文件操作",
            "variables": [
                {"name": "if", "params": "条件", "desc": "条件判断，支持==、>、<比较"},
                {"name": "path", "params": "路径", "desc": "获取文件路径"},
                {"name": "file", "params": "路径", "desc": "读取文件内容"}
            ]
        },
        "格式": {
            "title": "文本格式变量",
            "description": "用于文本格式化",
            "variables": [
                {"name": "code", "params": "语言,代码", "desc": "代码格式化"},
                {"name": "b", "params": "内容", "desc": "粗体"},
                {"name": "i", "params": "内容", "desc": "斜体"},
                {"name": "u", "params": "内容", "desc": "下划线"},
                {"name": "s", "params": "内容", "desc": "删除线"}
            ]
        }
    }
    
    # 如果没有指定类别，显示所有类别列表
    if not arg_text:
        result = "=== 自定义回复插件变量帮助 ===\n"
        result += "在回复内容中使用 [变量名]参数[/变量名] 格式可以实现动态内容\n\n"
        result += "变量分类：\n"
        
        for key, category in variable_categories.items():
            result += f"  {key} - {category['title']}\n"
        
        result += "\n查看详细说明请输入：#变量 <分类>\n"
        result += "例如：#变量 基础\n\n"
        result += "变量示例：\n"
        result += "  [at][sqq][/at] 你好，现在是 [t]HH:mm[/t]\n"
        result += "  掷骰子：[di][n]1-6[/n][/di]\n"
        result += "  随机选择：[rn]选项1,选项2,选项3[/rn]"
        
        await variables_help.finish(result)
    
    # 查找指定类别
    category_key = arg_text
    if category_key not in variable_categories:
        # 尝试模糊匹配
        for key in variable_categories.keys():
            if key.startswith(category_key):
                category_key = key
                break
        else:
            await variables_help.finish(f"未找到 {arg_text} 分类，可用分类：" + "、".join(variable_categories.keys()))
    
    # 显示指定类别的变量
    category = variable_categories[category_key]
    result = f"=== {category['title']} ===\n"
    result += f"{category['description']}\n\n"
    
    for var in category['variables']:
        params = f"[{var['params']}]" if var['params'] else ""
        result += f"  [{var['name']}]{params}[/{var['name']}] - {var['desc']}\n"
    
    result += "\n变量用法说明：\n"
    result += "  1. 变量可以嵌套使用，最大嵌套深度为5层\n"
    result += "  2. 部分变量需要参数，如 [n]1-100[/n]\n"
    result += "  3. 没有参数的变量可以省略结束标记，如 [br]"
    
    await variables_help.finish(result)

# 添加关键词
add_keyword = on_command("加词", priority=5, permission=SUPERUSER)

@add_keyword.handle()
async def handle_add_keyword(bot: Bot, event: Event, args: Message = CommandArg()):
    # 提取图片
    images = await extract_images_from_message(args)
    image_ids = []
    
    for image_data, filename in images:
        image_id = await data_manager.save_image(image_data)
        image_ids.append(image_id)
    
    # 处理纯文本参数和提取关键词
    arg_text = args.extract_plain_text().strip()
    
    # 检查是否包含消息段（表情等）
    has_segments = False
    for segment in args:
        if segment.type != "text" and segment.type != "image":
            has_segments = True
            break
    
    # 处理参数
    if arg_text:
        args_list = arg_text.split(" ", 2)
        
        # 正常带文本内容的情况
        if len(args_list) >= 1:
            keyword = args_list[0]
            mode = "fuzzy"  # 默认模式为模糊
            
            # 创建一个新的消息对象，用于保存内容
            content_parts = Message()
            
            # 从args复制所有段落，除了作为关键词的第一部分
            keyword_found = False
            for segment in args:
                if segment.type == "text":
                    text = segment.data["text"]
                    if not keyword_found and keyword in text:
                        # 移除关键词部分
                        remaining = text.replace(keyword, "", 1).lstrip()
                        if remaining:
                            content_parts.append(MessageSegment.text(remaining))
                        keyword_found = True
                    elif keyword_found:
                        # 关键词之后的文本
                        content_parts.append(segment)
                elif segment.type != "image":  # 不添加图片段，图片会通过image_ids单独处理
                    # 保留其他段落（如表情）
                    content_parts.append(segment)
            
            # 处理模式（精确/模糊）
            if len(args_list) > 1:
                if args_list[1] in ["精确", "模糊"]:
                    mode = "exact" if args_list[1] == "精确" else "fuzzy"
                    # 从内容中移除模式文本
                    for i, segment in enumerate(content_parts):
                        if segment.type == "text" and args_list[1] in segment.data["text"]:
                            text = segment.data["text"].replace(args_list[1], "", 1).lstrip()
                            if text:
                                content_parts[i] = MessageSegment.text(text)
                            else:
                                content_parts.pop(i)
                            break
            
            # 转换为字符串
            content = str(content_parts)
            
            # 如果内容为空但有图片，允许添加
            if not content and not image_ids and not has_segments:
                await add_keyword.finish("格式错误！正确格式：#加词 <词> <?模式> <内容>")
        else:
            await add_keyword.finish("格式错误！正确格式：#加词 <词> <?模式> <内容>")
    else:
        # 没有文本，检查是否有足够的图片/表情
        if not images and not has_segments:
            await add_keyword.finish("格式错误！纯图片/表情格式需要提供至少一个图片或表情。")
        
        # 使用第一张图片或第一个表情作为关键词
        keyword = "图片关键词" + str(random.randint(1000, 9999))  # 生成一个随机关键词标识
        mode = "fuzzy"
        
        # 使用完整的原始消息作为内容
        content = str(args)
    
    # 添加关键词
    success = await data_manager.add_keyword(keyword, mode, content, image_ids)
    
    if success:
        await add_keyword.finish(f"关键词 {keyword} 添加成功！")
    else:
        await add_keyword.finish(f"关键词 {keyword} 已存在！")

# 修改关键词
update_keyword = on_command("改词", priority=5, permission=SUPERUSER)

@update_keyword.handle()
async def handle_update_keyword(bot: Bot, event: Event, args: Message = CommandArg()):
    # 提取图片
    images = await extract_images_from_message(args)
    image_ids = []
    
    for image_data, filename in images:
        image_id = await data_manager.save_image(image_data)
        image_ids.append(image_id)
    
    # 处理纯文本参数和提取关键词
    arg_text = args.extract_plain_text().strip()
    
    # 检查是否包含消息段（表情等）
    has_segments = False
    for segment in args:
        if segment.type != "text" and segment.type != "image":
            has_segments = True
            break
    
    # 处理参数
    if arg_text:
        args_list = arg_text.split(" ", 2)
        
        if len(args_list) >= 1:
            keyword = args_list[0]
            mode = None  # 默认不修改模式
            
            # 创建一个新的消息对象，用于保存内容
            content_parts = Message()
            
            # 从args复制所有段落，除了作为关键词的第一部分
            keyword_found = False
            for segment in args:
                if segment.type == "text":
                    text = segment.data["text"]
                    if not keyword_found and keyword in text:
                        # 移除关键词部分
                        remaining = text.replace(keyword, "", 1).lstrip()
                        if remaining:
                            content_parts.append(MessageSegment.text(remaining))
                        keyword_found = True
                    elif keyword_found:
                        # 关键词之后的文本
                        content_parts.append(segment)
                elif segment.type != "image":  # 不添加图片段，图片会通过image_ids单独处理
                    # 保留其他段落（如表情）
                    content_parts.append(segment)
            
            # 处理模式（精确/模糊）
            if len(args_list) > 1:
                if args_list[1] in ["精确", "模糊"]:
                    mode = "exact" if args_list[1] == "精确" else "fuzzy"
                    # 从内容中移除模式文本
                    for i, segment in enumerate(content_parts):
                        if segment.type == "text" and args_list[1] in segment.data["text"]:
                            text = segment.data["text"].replace(args_list[1], "", 1).lstrip()
                            if text:
                                content_parts[i] = MessageSegment.text(text)
                            else:
                                content_parts.pop(i)
                            break
            
            # 转换为字符串
            content = str(content_parts)
            
            # 如果内容为空但有图片或其他段落，允许添加
            if not content and not image_ids and not has_segments:
                await update_keyword.finish("格式错误！正确格式：#改词 <词> <?模式> <内容>")
        else:
            await update_keyword.finish("格式错误！正确格式：#改词 <词> <?模式> <内容>")
    else:
        # 纯图片/表情情况，但必须提供要修改的关键词
        await update_keyword.finish("格式错误！必须提供要修改的关键词：#改词 <词> <?模式> <内容>")
    
    # 获取现有关键词数据，如果没有指定模式，则保留现有模式
    keyword_data = await data_manager.get_keyword_data(keyword)
    if keyword_data and mode is None:
        mode = keyword_data.mode
    elif mode is None:
        mode = "fuzzy"  # 默认为模糊模式
    
    # 更新关键词
    success = await data_manager.update_keyword(keyword, mode, content, image_ids)
    
    if success:
        await update_keyword.finish(f"关键词 {keyword} 修改成功！")
    else:
        await update_keyword.finish(f"关键词 {keyword} 不存在！")

# 删除关键词
delete_keyword = on_command("删词", priority=5, permission=SUPERUSER)

@delete_keyword.handle()
async def handle_delete_keyword(bot: Bot, event: Event, args: Message = CommandArg()):
    keyword = args.extract_plain_text().strip()
    
    if not keyword:
        await delete_keyword.finish("格式错误！正确格式：#删词 <词>")
    
    # 删除关键词
    success = await data_manager.delete_keyword(keyword)
    
    if success:
        await delete_keyword.finish(f"关键词 {keyword} 删除成功！")
    else:
        await delete_keyword.finish(f"关键词 {keyword} 不存在！")

# 查询关键词
query_keyword = on_command("查词", priority=5, permission=CUSTOM_REPLY_PERMISSION)

@query_keyword.handle()
async def handle_query_keyword(bot: Bot, event: Event, args: Message = CommandArg()):
    keyword = args.extract_plain_text().strip()
    
    if not keyword:
        await query_keyword.finish("格式错误！正确格式：#查词 <词>")
    
    # 获取关键词数据
    keyword_data = await data_manager.get_keyword_data(keyword)
    
    if not keyword_data:
        await query_keyword.finish(f"关键词 {keyword} 不存在！")
    
    # 构建回复信息
    mode_text = "精确匹配" if keyword_data.mode == "exact" else "模糊匹配"
    option_texts = []
    
    for i, option in enumerate(keyword_data.options):
        option_text = f"选项 {i+1}: {option.content}"
        if option.image_ids:
            option_text += f" [包含 {len(option.image_ids)} 张图片]"
        option_texts.append(option_text)
    
    result = f"关键词：{keyword}\n匹配模式：{mode_text}\n选项数量：{len(keyword_data.options)}\n\n"
    result += "\n".join(option_texts)
    
    await query_keyword.finish(result)

# 复制关键词
copy_keyword = on_command("复制词", priority=5, permission=SUPERUSER)

@copy_keyword.handle()
async def handle_copy_keyword(bot: Bot, event: Event, args: Message = CommandArg()):
    arg_text = args.extract_plain_text().strip()
    args_list = arg_text.split()
    
    if len(args_list) != 2:
        await copy_keyword.finish("格式错误！正确格式：#复制词 <原词> <新词>")
    
    source_keyword = args_list[0]
    target_keyword = args_list[1]
    
    # 复制关键词
    success = await data_manager.copy_keyword(source_keyword, target_keyword)
    
    if success:
        await copy_keyword.finish(f"关键词 {source_keyword} 已成功复制为 {target_keyword}！")
    else:
        await copy_keyword.finish(f"复制失败！请确保原词 {source_keyword} 存在且新词 {target_keyword} 不存在。")

# 修改关键词名称
rename_keyword = on_command("改词名", priority=5, permission=SUPERUSER)

@rename_keyword.handle()
async def handle_rename_keyword(bot: Bot, event: Event, args: Message = CommandArg()):
    arg_text = args.extract_plain_text().strip()
    args_list = arg_text.split()
    
    if len(args_list) != 2:
        await rename_keyword.finish("格式错误！正确格式：#改词名 <原词> <新词>")
    
    old_keyword = args_list[0]
    new_keyword = args_list[1]
    
    # 重命名关键词
    success = await data_manager.rename_keyword(old_keyword, new_keyword)
    
    if success:
        await rename_keyword.finish(f"关键词 {old_keyword} 已成功重命名为 {new_keyword}！")
    else:
        await rename_keyword.finish(f"重命名失败！请确保原词 {old_keyword} 存在且新词 {new_keyword} 不存在。")

# 添加关键词选项
add_option = on_command("加选项", priority=5, permission=SUPERUSER)

@add_option.handle()
async def handle_add_option(bot: Bot, event: Event, args: Message = CommandArg()):
    # 提取图片
    images = await extract_images_from_message(args)
    image_ids = []
    
    for image_data, filename in images:
        image_id = await data_manager.save_image(image_data)
        image_ids.append(image_id)
    
    # 处理纯文本参数和提取关键词
    arg_text = args.extract_plain_text().strip()
    
    # 检查是否包含消息段（表情等）
    has_segments = False
    for segment in args:
        if segment.type != "text" and segment.type != "image":
            has_segments = True
            break
    
    # 处理参数
    if arg_text:
        args_list = arg_text.split(" ", 1)
        
        if len(args_list) >= 1:
            keyword = args_list[0]
            
            # 创建一个新的消息对象，用于保存内容
            content_parts = Message()
            
            # 从args复制所有段落，除了作为关键词的第一部分
            keyword_found = False
            for segment in args:
                if segment.type == "text":
                    text = segment.data["text"]
                    if not keyword_found and keyword in text:
                        # 移除关键词部分
                        remaining = text.replace(keyword, "", 1).lstrip()
                        if remaining:
                            content_parts.append(MessageSegment.text(remaining))
                        keyword_found = True
                    elif keyword_found:
                        # 关键词之后的文本
                        content_parts.append(segment)
                elif segment.type != "image":  # 不添加图片段，图片会通过image_ids单独处理
                    # 保留其他段落（如表情）
                    content_parts.append(segment)
            
            # 转换为字符串
            content = str(content_parts)
            
            # 如果内容为空但有图片，允许添加
            if not content and not image_ids and not has_segments:
                await add_option.finish("格式错误！正确格式：#加选项 <词> <内容>")
        else:
            await add_option.finish("格式错误！正确格式：#加选项 <词> <内容>")
    else:
        # 纯图片/表情情况
        if not images and not has_segments:
            await add_option.finish("格式错误！正确格式：#加选项 <词> <内容> 或 包含图片/表情")
        await add_option.finish("格式错误！请提供关键词：#加选项 <词> <内容>")
    
    # 添加选项
    success = await data_manager.add_keyword_option(keyword, content, image_ids)
    
    if success:
        # 获取当前选项数量
        keyword_data = await data_manager.get_keyword_data(keyword)
        option_count = len(keyword_data.options) if keyword_data else 0
        await add_option.finish(f"关键词 {keyword} 的回复选项添加成功！当前共有 {option_count} 个选项。")
    else:
        await add_option.finish(f"关键词 {keyword} 不存在！")

# 修改关键词选项
update_option = on_command("改选项", priority=5, permission=SUPERUSER)

@update_option.handle()
async def handle_update_option(bot: Bot, event: Event, args: Message = CommandArg()):
    # 提取图片
    images = await extract_images_from_message(args)
    image_ids = []
    
    for image_data, filename in images:
        image_id = await data_manager.save_image(image_data)
        image_ids.append(image_id)
    
    # 处理纯文本参数和提取关键词
    arg_text = args.extract_plain_text().strip()
    
    # 检查是否包含消息段（表情等）
    has_segments = False
    for segment in args:
        if segment.type != "text" and segment.type != "image":
            has_segments = True
            break
    
    # 处理参数
    if not arg_text or len(arg_text.split(" ", 2)) < 2:
        await update_option.finish("格式错误！正确格式：#改选项 <词> <序号> <内容>")
    
    args_list = arg_text.split(" ", 2)
    keyword = args_list[0]
    
    try:
        index = int(args_list[1]) - 1  # 用户输入的序号从1开始，转换为从0开始的索引
        if index < 0:
            raise ValueError()
    except ValueError:
        await update_option.finish("序号必须是正整数！")
    
    # 创建一个新的消息对象，用于保存内容
    content_parts = Message()
    
    # 分析原始消息，找出关键词和序号之后的内容
    keyword_and_index_found = False
    for segment in args:
        if segment.type == "text":
            text = segment.data["text"]
            # 检查是否包含关键词和序号
            if not keyword_and_index_found and keyword in text and args_list[1] in text:
                # 找到关键词和序号后的文本
                parts = text.split(args_list[1], 1)
                if len(parts) > 1:
                    remaining = parts[1].lstrip()
                    if remaining:
                        content_parts.append(MessageSegment.text(remaining))
                keyword_and_index_found = True
            elif keyword_and_index_found:
                # 关键词和序号之后的文本
                content_parts.append(segment)
        elif segment.type != "image" and keyword_and_index_found:  # 不添加图片段，图片会通过image_ids单独处理
            # 保留其他段落（如表情）
            content_parts.append(segment)
    
    # 转换为字符串
    content = str(content_parts)
    
    # 如果没有内容但有图片或其他段落，允许修改
    if not content and not image_ids and not has_segments:
        # 如果至少有第三个参数，尝试使用
        if len(args_list) > 2:
            content = args_list[2]
        else:
            await update_option.finish("格式错误！正确格式：#改选项 <词> <序号> <内容>")
    
    # 更新选项
    success = await data_manager.update_keyword_option(keyword, index, content, image_ids)
    
    if success:
        await update_option.finish(f"关键词 {keyword} 的第 {index+1} 个回复选项修改成功！")
    else:
        await update_option.finish(f"修改失败！请确保关键词 {keyword} 存在且序号有效。")

# 删除关键词选项
delete_option = on_command("删选项", priority=5, permission=SUPERUSER)

@delete_option.handle()
async def handle_delete_option(bot: Bot, event: Event, args: Message = CommandArg()):
    arg_text = args.extract_plain_text().strip()
    args_list = arg_text.split()
    
    if len(args_list) != 2:
        await delete_option.finish("格式错误！正确格式：#删选项 <词> <序号>")
    
    keyword = args_list[0]
    
    try:
        index = int(args_list[1]) - 1  # 用户输入的序号从1开始，转换为从0开始的索引
        if index < 0:
            raise ValueError()
    except ValueError:
        await delete_option.finish("序号必须是正整数！")
    
    # 删除选项
    success = await data_manager.delete_keyword_option(keyword, index)
    
    if success:
        await delete_option.finish(f"关键词 {keyword} 的第 {index+1} 个回复选项删除成功！")
    else:
        await delete_option.finish(f"删除失败！请确保关键词 {keyword} 存在且序号有效。")

# 修改关键词匹配模式
change_mode = on_command("改模式", priority=5, permission=SUPERUSER)

@change_mode.handle()
async def handle_change_mode(bot: Bot, event: Event, args: Message = CommandArg()):
    arg_text = args.extract_plain_text().strip()
    args_list = arg_text.split()
    
    if len(args_list) != 2:
        await change_mode.finish("格式错误！正确格式：#改模式 <词> <模式>")
    
    keyword = args_list[0]
    mode_text = args_list[1]
    
    if mode_text not in ["精确", "模糊"]:
        await change_mode.finish("模式必须是\"精确\"或\"模糊\"！")
    
    mode = "exact" if mode_text == "精确" else "fuzzy"
    
    # 修改模式
    success = await data_manager.change_keyword_mode(keyword, mode)
    
    if success:
        await change_mode.finish(f"关键词 {keyword} 的匹配模式已修改为{mode_text}！")
    else:
        await change_mode.finish(f"关键词 {keyword} 不存在！")

# 添加定时消息
add_timer = on_command("加定时", priority=5, permission=SUPERUSER)

@add_timer.handle()
async def handle_add_timer(bot: Bot, event: Event, args: Message = CommandArg()):
    arg_text = args.extract_plain_text().strip()
    args_list = arg_text.split(" ", 2)
    
    if len(args_list) != 3:
        await add_timer.finish("格式错误！正确格式：#加定时 <名称> <时间> <内容>")
    
    name = args_list[0]
    time_str = args_list[1]
    content = args_list[2]
    
    # 验证时间格式
    if not re.match(r"^\d{2}:\d{2}$", time_str):
        await add_timer.finish("时间格式错误！正确格式：HH:MM，例如08:00")
    
    # 获取聊天类型和ID
    chat_type = "group" if isinstance(event, GroupMessageEvent) else "private"
    chat_id = event.group_id if chat_type == "group" else event.user_id
    
    # 提取图片
    images = await extract_images_from_message(args)
    image_ids = []
    
    for image_data, filename in images:
        image_id = await data_manager.save_image(image_data)
        image_ids.append(image_id)
    
    # 添加定时消息
    success = await data_manager.add_timer(name, time_str, content, image_ids, chat_type, chat_id)
    
    if success:
        await add_timer.finish(f"定时消息 {name} 添加成功！将在每天 {time_str} 发送。")
    else:
        await add_timer.finish(f"定时消息 {name} 已存在！")

# 删除定时消息
delete_timer = on_command("删定时", priority=5, permission=SUPERUSER)

@delete_timer.handle()
async def handle_delete_timer(bot: Bot, event: Event, args: Message = CommandArg()):
    name = args.extract_plain_text().strip()
    
    if not name:
        await delete_timer.finish("格式错误！正确格式：#删定时 <名称>")
    
    # 获取聊天类型和ID
    chat_type = "group" if isinstance(event, GroupMessageEvent) else "private"
    chat_id = event.group_id if chat_type == "group" else event.user_id
    
    # 删除定时消息
    success = await data_manager.delete_timer(chat_type, chat_id, name)
    
    if success:
        await delete_timer.finish(f"定时消息 {name} 删除成功！")
    else:
        await delete_timer.finish(f"定时消息 {name} 不存在！")

# 查看关键词列表
keyword_list = on_command("词列表", priority=5, permission=CUSTOM_REPLY_PERMISSION)

@keyword_list.handle()
async def handle_keyword_list(bot: Bot, event: Event, args: Message = CommandArg()):
    page_str = args.extract_plain_text().strip()
    
    # 默认为第一页
    page = 1
    if page_str:
        try:
            page = int(page_str)
            if page < 1:
                raise ValueError()
        except ValueError:
            await keyword_list.finish("页码必须是正整数！")
    
    # 每页显示的关键词数量
    page_size = 10
    
    # 获取所有关键词
    keywords = list(data_manager.data.keywords.keys())
    
    # 计算总页数
    total_pages = (len(keywords) + page_size - 1) // page_size
    
    if total_pages == 0:
        await keyword_list.finish("当前没有关键词！")
    
    if page > total_pages:
        await keyword_list.finish(f"页码超出范围！总页数：{total_pages}")
    
    # 获取当前页的关键词
    start_idx = (page - 1) * page_size
    end_idx = min(start_idx + page_size, len(keywords))
    current_page_keywords = keywords[start_idx:end_idx]
    
    # 构建回复消息
    result = f"关键词列表（第 {page}/{total_pages} 页）：\n\n"
    
    for i, keyword in enumerate(current_page_keywords):
        keyword_data = data_manager.data.keywords[keyword]
        mode_text = "精确匹配" if keyword_data.mode == "exact" else "模糊匹配"
        result += f"{i+1+start_idx}. {keyword} [{mode_text}] - {len(keyword_data.options)}个选项\n"
    
    result += f"\n总关键词数：{len(keywords)}"
    
    await keyword_list.finish(result)

# 查看定时消息列表
timer_list = on_command("定时列表", priority=5, permission=CUSTOM_REPLY_PERMISSION)

@timer_list.handle()
async def handle_timer_list(bot: Bot, event: Event, args: Message = CommandArg()):
    page_str = args.extract_plain_text().strip()
    
    # 默认为第一页
    page = 1
    if page_str:
        try:
            page = int(page_str)
            if page < 1:
                raise ValueError()
        except ValueError:
            await timer_list.finish("页码必须是正整数！")
    
    # 获取聊天类型和ID
    chat_type = "group" if isinstance(event, GroupMessageEvent) else "private"
    chat_id = event.group_id if chat_type == "group" else event.user_id
    
    # 筛选当前聊天环境的定时消息
    timers = []
    for timer_key, timer_data in data_manager.data.timers.items():
        if timer_data.chat_type == chat_type and timer_data.chat_id == chat_id:
            timers.append(timer_data)
    
    # 每页显示的定时消息数量
    page_size = 10
    
    # 计算总页数
    total_pages = (len(timers) + page_size - 1) // page_size
    
    if total_pages == 0:
        await timer_list.finish("当前没有定时消息！")
    
    if page > total_pages:
        await timer_list.finish(f"页码超出范围！总页数：{total_pages}")
    
    # 获取当前页的定时消息
    start_idx = (page - 1) * page_size
    end_idx = min(start_idx + page_size, len(timers))
    current_page_timers = timers[start_idx:end_idx]
    
    # 构建回复消息
    result = f"定时消息列表（第 {page}/{total_pages} 页）：\n\n"
    
    for i, timer in enumerate(current_page_timers):
        result += f"{i+1+start_idx}. {timer.name} - {timer.time}\n"
    
    result += f"\n总定时消息数：{len(timers)}"
    
    await timer_list.finish(result)

# 开启自定义回复功能
enable_custom_reply = on_command("开自定义", priority=5, permission=SUPERUSER)

@enable_custom_reply.handle()
async def handle_enable_custom_reply(bot: Bot, event: Event):
    if not isinstance(event, GroupMessageEvent):
        await enable_custom_reply.finish("该命令只能在群聊中使用！")
    
    group_id = event.group_id
    
    await data_manager.set_group_enabled(group_id, True)
    await enable_custom_reply.finish("已在当前群开启自定义回复功能！")

# 关闭自定义回复功能
disable_custom_reply = on_command("关自定义", priority=5, permission=SUPERUSER)

@disable_custom_reply.handle()
async def handle_disable_custom_reply(bot: Bot, event: Event):
    if not isinstance(event, GroupMessageEvent):
        await disable_custom_reply.finish("该命令只能在群聊中使用！")
    
    group_id = event.group_id
    
    await data_manager.set_group_enabled(group_id, False)
    await disable_custom_reply.finish("已在当前群关闭自定义回复功能！")

# 查看统计数据
statistics = on_command("自定义统计", priority=5, permission=CUSTOM_REPLY_PERMISSION)

@statistics.handle()
async def handle_statistics(bot: Bot, event: Event):
    stats = await data_manager.get_keyword_statistics()
    
    result = "自定义回复插件统计：\n\n"
    for key, value in stats.items():
        result += f"{key}：{value}\n"
    
    await statistics.finish(result)

# 备份配置文件
backup = on_command("备份自定义", priority=5, permission=SUPERUSER)

@backup.handle()
async def handle_backup(bot: Bot, event: Event):
    backup_group = await data_manager.get_backup_group()
    
    if backup_group is None:
        await backup.finish("未设置备份群！请使用 #设置备份群 命令设置。")
    
    # 创建临时备份文件
    import json
    import os
    from datetime import datetime
    
    backup_time = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_filename = f"custom_reply_backup_{backup_time}.json"
    backup_path = config.custom_reply_data_dir / backup_filename
    
    # 将数据写入备份文件
    async with aiofiles.open(backup_path, "w", encoding="utf-8") as f:
        await f.write(data_manager.data.json(ensure_ascii=False, indent=4))
    
    # 发送备份文件
    await bot.call_api(
        "upload_group_file",
        group_id=backup_group,
        file=str(backup_path.absolute()),
        name=backup_filename
    )
    
    # 删除临时文件
    os.remove(backup_path)
    
    await backup.finish(f"配置文件已备份到群 {backup_group}！")

# 重新加载配置
reload_config = on_command("重载自定义", priority=5, permission=SUPERUSER)

@reload_config.handle()
async def handle_reload_config(bot: Bot, event: Event):
    await data_manager.load_data()
    await reload_config.finish("配置已重新加载！")

# 设置备份群
set_backup_group = on_command("设置备份群", priority=5, permission=SUPERUSER)

@set_backup_group.handle()
async def handle_set_backup_group(bot: Bot, event: Event):
    if not isinstance(event, GroupMessageEvent):
        await set_backup_group.finish("该命令只能在群聊中使用！")
    
    group_id = event.group_id
    
    await data_manager.set_backup_group(group_id)
    await set_backup_group.finish(f"已将群 {group_id} 设置为备份群！")

# 关键词响应器
keyword_matcher = on_message(priority=100, permission=NOT_BLACKLISTED, block=False)

@keyword_matcher.handle()
async def handle_keyword_message(bot: Bot, event: Event):
    # 过滤自己发送的消息
    if hasattr(event, "self_id") and event.self_id == event.user_id:
        return
    
    # 获取消息文本
    if not hasattr(event, "message") or not hasattr(event, "get_message"):
        return
    
    message = event.get_message()
    message_text = message.extract_plain_text()
    
    # 检查是否为命令消息，如果是则不触发关键词回复
    if message_text and message_text.startswith("#"):
        return
    
    # 检查群组是否启用
    if hasattr(event, "group_id"):
        is_enabled = await data_manager.is_group_enabled(event.group_id)
        if not is_enabled:
            return
    
    # 获取所有关键词
    keywords = list(data_manager.data.keywords.keys())
    
    # 用于存储不同匹配类型的关键词
    exact_matches = []  # 精确匹配
    fuzzy_matches = []  # 模糊匹配，按长度排序
    
    # 记录消息字符串形式，用于日志和调试
    message_str = str(message)
    logger = logging.getLogger("custom_reply")
    logger.info(f"处理消息: {message_str}")
    
    # 提取原始消息中的表情
    faces_in_message = []
    for segment in message:
        if segment.type == "face" and hasattr(segment, "data") and "id" in segment.data:
            faces_in_message.append(str(segment))
    
    # 遍历所有关键词进行匹配
    for keyword in keywords:
        keyword_data = data_manager.data.keywords.get(keyword)
        if not keyword_data:
            continue
        
        # 根据匹配模式进行匹配
        is_matched = False
        
        # 检查包含表情的情况
        if any(segment.type == "face" for segment in message):
            # 表情处理逻辑 - 表情必须精确匹配
            
            # 如果关键词中包含CQ码（表情），进行比较
            if keyword_data.mode == "exact":
                # 精确模式：整个消息必须完全匹配
                is_matched = keyword == message_str
                if is_matched:
                    logger.info(f"精确匹配表情消息: {keyword}")
                    exact_matches.append(keyword)
            else:  # fuzzy
                # 模糊模式：关键词是否在消息中
                is_matched = keyword in message_str or (
                    "[CQ:face" in keyword and any(face_code in message_str for face_code in keyword.split())
                )
                if is_matched:
                    logger.info(f"模糊匹配表情消息: {keyword}")
                    fuzzy_matches.append(keyword)
        else:
            # 文本匹配逻辑
            if keyword_data.mode == "exact":
                is_matched = keyword == message_text
                if is_matched:
                    logger.info(f"精确匹配文本消息: {keyword}")
                    exact_matches.append(keyword)
            else:  # fuzzy
                is_matched = keyword in message_text
                if is_matched:
                    logger.info(f"模糊匹配文本消息: {keyword}")
                    fuzzy_matches.append(keyword)
    
    # 按优先级和长度排序关键词
    # 1. 精确匹配优先
    # 2. 对于模糊匹配，较长的关键词优先
    matched_keywords = exact_matches + sorted(fuzzy_matches, key=len, reverse=True)
    
    # 如果没有匹配到关键词，直接返回
    if not matched_keywords:
        return
    
    logger.info(f"匹配到的关键词(按优先级): {matched_keywords}")
    
    # 选择最高优先级的关键词（第一个）
    if len(matched_keywords) > 1:
        # 有多个匹配项，随机选择一个（但精确匹配优先）
        if exact_matches:
            # 如果有精确匹配，只从精确匹配中选择
            keyword = random.choice(exact_matches)
            logger.info(f"从精确匹配中随机选择: {keyword}")
        else:
            # 模糊匹配，优先选择较长的关键词
            # 先获取最长的关键词长度
            max_length = len(matched_keywords[0])
            longest_keywords = [k for k in matched_keywords if len(k) == max_length]
            
            # 从最长的关键词中随机选择一个
            keyword = random.choice(longest_keywords)
            logger.info(f"从最长的模糊匹配中随机选择: {keyword}, 长度: {max_length}")
    else:
        # 只有一个匹配项
        keyword = matched_keywords[0]
        logger.info(f"只有一个匹配项: {keyword}")
    
    # 获取随机回复
    response = await data_manager.get_random_response(keyword)
    if not response:
        return
    
    content, image_ids = response
    
    # 解析变量
    extra_vars = {"input_text": message_text, "raw_input_text": message_text}
    parsed_content = await variable_parser.parse(content, bot, event, extra_vars)
    
    # 构建消息
    message = await build_message(parsed_content, image_ids)
    
    # 根据聊天类型添加延迟
    if isinstance(event, GroupMessageEvent):
        delay = random.randint(*config.custom_reply_group_delay) / 1000
    else:
        delay = random.randint(*config.custom_reply_private_delay) / 1000
    
    await asyncio.sleep(delay)
    
    # 发送消息
    if isinstance(event, GroupMessageEvent):
        await bot.call_api(
            "send_group_msg",
            group_id=event.group_id,
            message=message
        )
    elif isinstance(event, PrivateMessageEvent):
        await bot.call_api(
            "send_private_msg",
            user_id=event.user_id,
            message=message
        )

# 测试随机回复命令
test_random = on_command("测试随机回复", priority=5, permission=SUPERUSER)

@test_random.handle()
async def handle_test_random(bot: Bot, event: Event, args: Message = CommandArg()):
    keyword = args.extract_plain_text().strip()
    
    if not keyword:
        await test_random.finish("请提供要测试的关键词")
    
    # 获取关键词数据
    keyword_data = await data_manager.get_keyword_data(keyword)
    
    if not keyword_data:
        await test_random.finish(f"关键词 {keyword} 不存在！")
    
    # 显示所有选项
    option_texts = []
    for i, option in enumerate(keyword_data.options):
        option_text = f"选项 {i+1}: {option.content}"
        if option.image_ids:
            option_text += f" [包含 {len(option.image_ids)} 张图片]"
        option_texts.append(option_text)
    
    result = f"关键词：{keyword}\n选项数量：{len(keyword_data.options)}\n\n"
    result += "\n".join(option_texts)
    result += "\n\n测试随机选择10次："
    
    # 保存每个选项被选中的次数
    option_counts = [0] * len(keyword_data.options)
    
    # 测试随机选择10次
    for i in range(10):
        response = await data_manager.get_random_response(keyword)
        if response:
            content, image_ids = response
            
            # 找出是哪个选项
            option_index = -1
            for j, option in enumerate(keyword_data.options):
                if option.content == content and set(option.image_ids) == set(image_ids):
                    option_index = j
                    option_counts[j] += 1
                    break
            
            # 截取内容的前20个字符，避免过长
            content_preview = content[:20] + ("..." if len(content) > 20 else "")
            result += f"\n{i+1}. 选项 {option_index + 1}: {content_preview}"
        else:
            result += f"\n{i+1}. 获取随机回复失败"
    
    # 添加选中统计
    result += "\n\n选项选中统计："
    for i, count in enumerate(option_counts):
        result += f"\n选项 {i+1}: 被选中 {count} 次"
    
    await test_random.finish(result)

# 添加一个更详细的调试命令
debug_random = on_command("调试随机", priority=5, permission=SUPERUSER)

@debug_random.handle()
async def handle_debug_random(bot: Bot, event: Event, args: Message = CommandArg()):
    keyword = args.extract_plain_text().strip()
    
    if not keyword:
        await debug_random.finish("请提供要调试的关键词")
    
    # 获取关键词数据
    keyword_data = await data_manager.get_keyword_data(keyword)
    
    if not keyword_data:
        await debug_random.finish(f"关键词 {keyword} 不存在！")
    
    # 显示相关信息
    result = f"=== 关键词调试信息 ===\n"
    result += f"关键词: {keyword}\n"
    result += f"匹配模式: {'精确' if keyword_data.mode == 'exact' else '模糊'}\n"
    result += f"选项数量: {len(keyword_data.options)}\n\n"
    
    # 显示所有选项
    for i, option in enumerate(keyword_data.options):
        result += f"选项 {i+1}:\n"
        result += f"  - 内容: {option.content}\n"
        result += f"  - 图片: {len(option.image_ids)} 张\n"
        if option.image_ids:
            result += f"  - 图片ID: {', '.join(option.image_ids[:3])}{'...' if len(option.image_ids) > 3 else ''}\n"
    
    # 随机种子信息
    current_time = int(time.time())
    result += f"\n随机种子信息:\n"
    result += f"当前时间戳: {current_time}\n"
    result += f"随机种子值: {current_time % (2**32)}\n"
    
    await debug_random.finish(result)

# 添加一个测试关键词匹配的命令
test_match = on_command("测试匹配", priority=5, permission=SUPERUSER)

@test_match.handle()
async def handle_test_match(bot: Bot, event: Event, args: Message = CommandArg()):
    # 原始消息和纯文本
    original_message = args
    message_text = args.extract_plain_text().strip()
    
    if not message_text and len(original_message) == 0:
        await test_match.finish("请提供要测试的消息内容")
    
    # 完整消息字符串
    message_str = str(original_message)
    
    # 获取所有关键词
    keywords = list(data_manager.data.keywords.keys())
    
    # 用于存储不同匹配类型的关键词
    exact_matches = []  # 精确匹配
    fuzzy_matches = []  # 模糊匹配
    
    # 检查是否包含表情或其他特殊段
    has_special_segments = False
    for segment in original_message:
        if segment.type != "text":
            has_special_segments = True
            break
    
    # 遍历所有关键词进行匹配
    for keyword in keywords:
        keyword_data = data_manager.data.keywords.get(keyword)
        if not keyword_data:
            continue
        
        # 根据匹配模式进行匹配
        is_matched = False
        
        # 检查是否包含表情等特殊段落
        if has_special_segments:
            # 如果关键词中包含CQ码，进行比较
            if keyword_data.mode == "exact":
                # 精确模式：整个消息必须完全匹配
                is_matched = keyword == message_str
                if is_matched:
                    exact_matches.append(keyword)
            else:  # fuzzy
                # 模糊模式：关键词是否在消息中
                is_matched = keyword in message_str or (
                    "[CQ:" in keyword and "[CQ:" in message_str
                )
                if is_matched:
                    fuzzy_matches.append(keyword)
        else:
            # 文本匹配逻辑
            if keyword_data.mode == "exact":
                is_matched = keyword == message_text
                if is_matched:
                    exact_matches.append(keyword)
            else:  # fuzzy
                is_matched = keyword in message_text
                if is_matched:
                    fuzzy_matches.append(keyword)
    
    # 按优先级和长度排序关键词
    # 1. 精确匹配优先
    # 2. 对于模糊匹配，较长的关键词优先
    sorted_fuzzy_matches = sorted(fuzzy_matches, key=len, reverse=True)
    matched_keywords = exact_matches + sorted_fuzzy_matches
    
    # 构建回复消息
    result = f"测试消息: {message_str}\n"
    if has_special_segments:
        result += "消息类型: 包含表情或特殊段落\n"
    else:
        result += "消息类型: 纯文本\n"
    result += "\n"
    
    if not matched_keywords:
        result += "未匹配到任何关键词"
    else:
        result += "匹配结果:\n"
        result += "精确匹配:\n"
        if exact_matches:
            for i, keyword in enumerate(exact_matches):
                result += f"{i+1}. {keyword}\n"
        else:
            result += "  无\n"
        
        result += "\n模糊匹配 (按长度排序):\n"
        if sorted_fuzzy_matches:
            for i, keyword in enumerate(sorted_fuzzy_matches):
                keyword_data = data_manager.data.keywords.get(keyword)
                option_count = len(keyword_data.options) if keyword_data else 0
                result += f"{i+1}. {keyword} (长度: {len(keyword)}, 选项数: {option_count})\n"
        else:
            result += "  无\n"
        
        result += "\n最终会选择: "
        if exact_matches:
            if len(exact_matches) == 1:
                result += f"{exact_matches[0]} (精确匹配)"
            else:
                result += f"从 {', '.join(exact_matches)} 中随机选择一个 (都是精确匹配)"
        elif sorted_fuzzy_matches:
            max_length = len(sorted_fuzzy_matches[0])
            longest_keywords = [k for k in sorted_fuzzy_matches if len(k) == max_length]
            if len(longest_keywords) == 1:
                result += f"{longest_keywords[0]} (最长的模糊匹配, 长度: {max_length})"
            else:
                result += f"从 {', '.join(longest_keywords)} 中随机选择一个 (同为最长的模糊匹配, 长度: {max_length})"
    
    await test_match.finish(result)

# 修复变量命令
fix_variables = on_command("修复变量", priority=5, permission=SUPERUSER)

@fix_variables.handle()
async def handle_fix_variables(bot: Bot, event: Event):
    """修复被错误编码的变量标记"""
    fixed_count = await data_manager.fix_escaped_variables()
    
    if fixed_count > 0:
        await fix_variables.finish(f"修复成功！已修复 {fixed_count} 条被错误编码的变量标记。\n请重新加载数据：#重载自定义")
    else:
        await fix_variables.finish("没有找到需要修复的变量标记。")

# 注册启动事件
driver = get_driver()

@driver.on_startup
async def init_data():
    """初始化数据"""
    await data_manager.load_data()
    logger = get_driver().logger
    logger.info("自定义回复插件数据加载完成")

# 导入正则表达式模块
import re 