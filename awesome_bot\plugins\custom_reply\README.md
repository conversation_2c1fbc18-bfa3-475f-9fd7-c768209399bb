# 自定义回复插件使用指南 🤖

## 安装步骤 📥
2. 添加插件配置到 `.env` 文件：
```ini
# 自定义回复插件配置
CUSTOM_REPLY_SUPERUSERS=["123456789"]  # 超级管理员QQ号，只有超级管理员可以使用自定义的命令（多个QQ号用逗号隔开）
CUSTOM_REPLY_DEFAULT_ENABLED=false  # 默认启用状态，设置为true则插件在启动时自动启用
CUSTOM_REPLY_MAX_TRIGGER=2  # 单条消息最大触发关键词数，默认2个
CUSTOM_REPLY_BLACKLIST=[2854196310]  # 全局QQ黑名单，被列入黑名单的QQ号的消息将被忽略（示例 QQ 是 Q 群管家）
CUSTOM_REPLY_GROUP_DELAY=[300,3000]  # 群聊延迟区间(毫秒)，默认300-3000毫秒
CUSTOM_REPLY_PRIVATE_DELAY=[1000,6000]  # 私聊延迟区间(毫秒)，默认1000-6000毫秒
```

## 自定义指令列表 📋

| 默认命令或触发格式 | 功能与说明 |
|-------------------|------------|
| `#加词 <词> <?模式> <内容>` | 添加关键词回复，模式可选值：精确、模糊，模式可省，默认模糊。示例：`#加词 天气 模糊 今天天气很好` |
| `#改词 <词> <?模式> <内容>` | 修改(覆盖)关键词回复，模式可选值：精确、模糊，模式可省，默认模糊。示例：`#改词 天气 精确 明天天气会下雨` |
| `#删词 <词>` | 删除关键词回复。示例：`#删词 天气` |
| `#查词 <词>` | 查询关键词回复。示例：`#查词 天气` |
| `#复制词 <原词> <新词>` | 复制关键词。示例：`#复制词 天气 今日天气` |
| `#改词名 <原词> <新词>` | 修改关键词词名，回复内容与模式保持不变。示例：`#改词名 今日天气 明日天气` |
| `#加选项 <词> <内容>` | 添加回复选项，添加后关键词回复内容为随机选项中随机一个。示例：`#加选项 天气 明天会更好` |
| `#改选项 <词> <序号> <内容>` | 按选项序号修改已经存在的关键词回复选项。示例：`#改选项 天气 1 明天会下雪` |
| `#删选项 <词> <序号>` | 按选项序号删除已经存在的关键词回复选项。示例：`#删选项 天气 1` |
| `#改模式 <词> <模式>` | 修改关键词匹配模式，模式可选值：精确、模糊。示例：`#改模式 天气 精确` |
| `#加定时 <名称> <时间> <内容>` | 添加当前对话环境下的定时消息，时间格式：08:00。示例：`#加定时 早安 08:00 早上好，大家` |
| `#删定时 <名称>` | 删除定时消息。示例：`#删定时 早安` |
| `#词列表 <?页码>` | 查看关键词列表，默认第一页。示例：`#词列表 1` |
| `#定时列表 <?页码>` | 查看定时消息列表，默认第一页。示例：`#定时列表 1` |
| `#开自定义` | 在当前群开启自定义话术功能。示例：`#开自定义` |
| `#关自定义` | 在当前群关闭自定义话术功能。示例：`#关自定义` |
| `#自定义统计` | 查看自定义词条和定时消息的统计数据。示例：`#自定义统计` |
| `#备份自定义` | 将配置文件上传至指定群。示例：`#备份自定义` |
| `#重载自定义` | 重新读取本地配置文件。示例：`#重载自定义` |
| `#设置备份群` | 设置当前群为备份配置文件的目标群。示例：`#设置备份群` |
| `#自定义` | 查看自定义话术的所有命令格式列表。示例：`#自定义` |
| `#自定义 帮助` | 查看自定义话术的所有命令的详细用法。示例：`#自定义 帮助` |

## 变量系统详解 🔧

### 基础文本变量
| 变量格式 | 变量类型 | 说明 | 示例 |
|---------|---------|------|------|
| `[br]` | 换行符 | 文本换行 | `第一行[br]第二行` |
| `[space]` | 空格 | 插入空格 | `Hello[space]World` |
| `[n]1-100[/n]` | 随机数 | 生成指定范围随机数 | `[n]1-10[/n]` |
| `[in]` | 输入文本 | 获取触发词后的文本(转义) | `你说了:[in]` |
| `[IN]` | 原始输入 | 获取触发词后的文本(不转义) | `原文:[IN]` |
| `[rf]` | 随机表情 | 随机QQ表情 | `来个表情[rf]` |
| `[rt]` | 随机一言 | 随机一言 | `今日一言:[rt]` |
| `[rc]` | 撤回 | 撤回当前消息 | `这条消息将被撤回[rc]` |

### 群组相关变量
| 变量格式 | 变量类型 | 说明 | 示例 |
|---------|---------|------|------|
| `[gid]` | 群号 | 当前群号 | `[gid]` |
| `[gnick]` | 群名片 | 发送者群名片 | `[gnick]` |
| `[title]` | 群头衔 | 发送者群头衔 | `[title]` |
| `[level]` | 群等级 | 发送者群等级 | `[level]` |
| `[radmin]` | 随机管理 | 随机选择一位管理员 | `[radmin]` |
| `[owner]` | 群主 | 群主QQ号 | `[owner]` |
| `[memcount]` | 成员数 | 当前群成员数 | `[memcount]` |
| `[gmc]qq[/gmc]` | 群名片 | 获取指定QQ的群名片 | `[gmc]123456[/gmc]` |
| `[jointime]qq[/jointime]` | 入群时间 | 获取入群时间 | `[jointime]123456[/jointime]` |
| `[lastmsg]qq[/lastmsg]` | 最后发言 | 最后发言时间 | `[lastmsg]123456[/lastmsg]` |

### 用户交互变量
| 变量格式 | 变量类型 | 说明 | 示例 |
|---------|---------|------|------|
| `[bqq]` | 机器人QQ | 机器人QQ号 | `[bqq]` |
| `[sqq]` | 发送者QQ | 发送者QQ号 | `[sqq]` |
| `[tqq]` | 被艾特QQ | 被@的人QQ号 | `[tqq]` |
| `[at]qq[/at]` | 艾特 | 艾特指定QQ | `[at]123456[/at]` |
| `[all]` | 全体 | 艾特全体成员 | `[all]` |
| `[rpl]` | 引用 | 引用回复 | `[rpl]` |
| `[pk]qq[/pk]` | 戳一戳 | 戳一戳指定QQ | `[pk]123456[/pk]` |
| `[b]qq-分钟[/b]` | 禁言 | 禁言指定QQ指定时间 | `[b]123456-10[/b]` |
| `[av]qq[/av]` | 头像 | 获取指定QQ的头像 | `[av]123456[/av]` |
| `[rgu]n-m[/rgu]` | 随机群友 | 随机抽取n-m号群友 | `[rgu]1-3[/rgu]` |

### Cookie相关变量
| 变量格式 | 变量类型 | 说明 | 示例 |
|---------|---------|------|------|
| `[bkn]` | CSRF令牌 | QQ空间CSRF令牌 | `[bkn]` |
| `[skey]` | SKEY | QQ SKEY值 | `[skey]` |
| `[p_skey]` | P_SKEY | QQ P_SKEY值 | `[p_skey]` |
| `[cookie]` | Cookie | 完整Cookie | `[cookie]` |

### 多媒体变量
| 变量格式 | 变量类型 | 说明 | 示例 |
|---------|---------|------|------|
| `[xml]内容[/xml]` | XML消息 | 发送XML消息 | `[xml]<msg>内容</msg>[/xml]` |
| `[json]内容[/json]` | JSON消息 | 发送JSON消息 | `[json]{"type":"text"}[/json]` |
| `[music]id[/music]` | 音乐 | 发送音乐分享 | `[music]123456[/music]` |
| `[record]url[/record]` | 语音 | 发送语音 | `[record]URL[/record]` |
| `[video]url[/video]` | 视频 | 发送视频 | `[video]URL[/video]` |
| `[image]url[/image]` | 图片 | 发送图片 | `[image]URL[/image]` |
| `[rp]类型,标题[/rp]` | 红包 | 发送红包卡片 | `[rp]普通,新年快乐[/rp]` |
| `[location]经度,纬度,标题[/location]` | 位置 | 发送位置分享 | `[location]116.4,39.9,北京[/location]` |
| `[link]url,标题,图片[/link]` | 链接 | 发送链接分享 | `[link]URL,标题,图片[/link]` |

### 游戏互动变量
| 变量格式 | 变量类型 | 说明 | 示例 |
|---------|---------|------|------|
| `[di]点数[/di]` | 骰子 | 投掷指定点数骰子 | `[di]6[/di]` |
| `[rps]类型[/rps]` | 猜拳 | 出拳(1石头2剪刀3布) | `[rps]1[/rps]` |
| `[rn]选项1,选项2[/rn]` | 随机选择 | 随机选择一个选项 | `[rn]早安,午安,晚安[/rn]` |

### 文本格式变量
| 变量格式 | 变量类型 | 说明 | 示例 |
|---------|---------|------|------|
| `[code]语言,代码[/code]` | 代码块 | 代码格式 | `[code]python,print("Hello")[/code]` |
| `[table]内容[/table]` | 表格 | 表格格式 | `[table]内容[/table]` |
| `[font]字体[/font]` | 字体 | 指定字体 | `[font]微软雅黑[/font]` |
| `[color]颜色[/color]` | 颜色 | 文字颜色 | `[color]#FF0000[/color]` |
| `[b]内容[/b]` | 粗体 | 粗体文本 | `[b]粗体[/b]` |
| `[i]内容[/i]` | 斜体 | 斜体文本 | `[i]斜体[/i]` |
| `[u]内容[/u]` | 下划线 | 下划线文本 | `[u]下划线[/u]` |
| `[s]内容[/s]` | 删除线 | 删除线文本 | `[s]删除线[/s]` |

### 网络请求变量
| 变量格式 | 变量类型 | 说明 | 示例 |
|---------|---------|------|------|
| `[g]url[/g]` | GET请求 | 发送GET请求 | `[g]URL[/g]` |
| `[p]url\|data[/p]` | POST请求 | 发送POST请求 | `[p]URL\|{"key":"value"}[/p]` |
| `[url]url[/url]` | URL请求 | 发送URL请求 | `[url]URL[/url]` |

### 文件操作变量
| 变量格式 | 变量类型 | 说明 | 示例 |
|---------|---------|------|------|
| `[path]路径[/path]` | 文件路径 | 获取文件路径 | `[path]data/1.txt[/path]` |
| `[file]路径[/file]` | 文件内容 | 读取文件内容 | `[file]data/1.txt[/file]` |

### 时间格式参考
| 格式符 | 说明 | 示例 |
|--------|------|------|
| `YYYY` | 四位年份 | 2024 |
| `YY` | 两位年份 | 24 |
| `MM` | 两位月份 | 01-12 |
| `DD` | 两位日期 | 01-31 |
| `HH` | 24小时制 | 00-23 |
| `hh` | 12小时制 | 01-12 |
| `mm` | 两位分钟 | 00-59 |
| `ss` | 两位秒数 | 00-59 |

### 域名参考表
| 域名代码 | 实际域名 |
|---------|---------|
| `qun` | qun.qq.com |
| `qzone` | qzone.qq.com |
| `main` | qq.com |
| `game` | game.qq.com |
| `docs` | docs.qq.com |
| `vip` | vip.qq.com |
| `mail` | mail.qq.com |
| `music` | y.qq.com |
| `video` | v.qq.com |
| `weixin` | weixin.qq.com |
| `cloud` | cloud.tencent.com |
| `api` | api.qq.com |

## 实用技巧 💡

### 1. API对接示例 🌐

```bash
# 随机笑话
#加词 笑话 [g]http://lkaa.top/API/xiaohua/api.php?type=text[/g]

# 随机段子
#加词 段子 [g]https://www.hlapi.cn/api/gxdz[/g]

# 一言
#加词 一言 [g]https://v1.hitokoto.cn/?encode=text[/g]

# 舔狗日记
#加词 舔狗日记 [g]https://api.ixiaowai.cn/tgrj/index.php[/g]

# 随机美图
#加词 看妹子 [i][g]https://xiaobai.klizi.cn/API/img/beauty.php[/g][/i]

# 随机动漫
#加词 看动漫 [fi]http://tianyi.qrspeed.pro/api/ACG.php?type=image[/fi]
```

### 2. 本地随机回复 🎲

```bash
# 抽奖系统
#加词 抽奖 [at][sqq][/at] 抽到了老八汉堡！！
#加选项 抽奖 你抽到一张探囊取物
#加选项 抽奖 你抽到了一张交警罚单
#加选项 抽奖 你抽到了一张疫情隔离通知书
#加选项 抽奖 你抽到了一张北大通知书
#加选项 抽奖 你抽到了一张清华通知书
#加选项 抽奖 憋抽了，害搁这抽呐，都被你玩坏了
```

### 3. 个性化机器人 🤪

```bash
# 表情互动
#加词 哈哈 😄
#加词 😊 🥰

# 趣味对话
#加词 有人吗 没人，快滚 🙄
#加词 还不睡 你不也没睡，逼逼赖赖的 😪
#加词 戳我 [pk][sqq][/pk]
#加词 我超 我超啥啊我超 🤔
```

### 4. 变量组合玩法 🎮

```bash
# 骰子游戏
#加词 扔骰子 [di][n]1-6[/n][/di]

# 随机禁言
#加词 抽一个大冤种禁言 [b][rgu]-[n]1-3[/n][/b]好惨一冤种 😈

# 查看头像
#加词 我的头像 [av][sqq][/av]

# 签到系统
#加词 签到 [at][sqq][/at] 签到成功！[br]
时间：[t]YYYY-MM-DD HH:mm:ss[/t][br]
获得积分：[n]1-100[/n] 💰
```

### 5. 高级玩法示例 🎯

```bash
# 1. 群成员抽签系统
#加词 抽签 [at][sqq][/at] 抽到了第[n]1-100[/n]签！[br]
今日运势：[rt][br]
宜：[g]https://api.example.com/fortune/good[/g][br]
忌：[g]https://api.example.com/fortune/bad[/g][br]
幸运颜色：[rn]红色,蓝色,绿色,黄色,紫色,粉色,橙色,青色,金色,银色[/rn][br]
幸运数字：[n]0-9[/n][br]
今日一言：[g]https://v1.hitokoto.cn/?encode=text[/g]

# 2. 群活跃度排行
#加词 群榜 🏆 群活跃度排行 [br]
统计时间：[t]YYYY-MM-DD[/t][br]
[rgu]1-3[/rgu] 🥇 [gmc][rgu]1[/rgu][/gmc][br]
[rgu]4-6[/rgu] 🥈 [gmc][rgu]4[/rgu][/gmc][br]
[rgu]7-9[/rgu] 🥉 [gmc][rgu]7[/rgu][/gmc][br]
恭喜以上群友！[br]
[i]https://api.example.com/certificate?text=群活跃度排行[/i]

# 3. 群游戏-猜数字
#加词 猜数字 游戏开始！[br]
目标数字已生成(1-100)，请发送数字进行猜测[br]
提示：发送"猜 数字"进行猜测，如"猜 50"[br]
[n]1-100[/n]

#加词 猜 模糊 [at][sqq][/at] [br]
你猜的数字：[in][br]
[if][in]>[n]1-100[/n][/if]太大了！[br]
[if][in]<[n]1-100[/n][/if]太小了！[br]
[if][in]==[n]1-100[/n][/if]恭喜你猜对了！奖励：[n]10-100[/n]积分 🎉

# 4. 自定义表情包生成
#加词 表情包 [at][sqq][/at] 的专属表情来啦！[br]
[i]https://api.example.com/meme?qq=[sqq]&text=[in]&time=[t]HH:mm[/t][/i]

# 5. 群打卡排行榜
#加词 打卡排行 📊 群打卡排行榜 [br]
统计时间：[t]YYYY-MM-DD[/t][br]
[rgu]1-10[/rgu] [gmc][rgu]1-10[/rgu][/gmc] [n]1-100[/n]天[br]
[i]https://api.example.com/rank?group=[gid]&date=[t]YYYYMMDD[/t][/i]

# 6. 群友互动-真心话大冒险
#加词 真心话 [at][sqq][/at] 抽到了真心话：[br]
[rn]你最喜欢群里的哪个人？,你上一次哭是什么时候？,你的初恋是几岁？,你最近一次说谎是什么时候？,你最想实现的愿望是什么？,你最害怕什么？,你现在喜欢的人是谁？,你最后悔的事是什么？[/rn]

#加词 大冒险 [at][sqq][/at] 抽到了大冒险：[br]
[rn]给群主发一句"我爱你",修改群昵称一天,唱一首歌,发一张自拍,学猫叫三声,发一个土味情话,夸群里一位异性三句[/rn][br]
完成大冒险后奖励：[n]10-50[/n]积分 💰

# 7. 每日运势抽签
#加词 运势 [at][sqq][/at] [t]YYYY-MM-DD[/t] 的运势：[br]
🎯 运势指数：[n]0-100[/n]%[br]
💖 桃花指数：[n]0-100[/n]%[br]
💰 财运指数：[n]0-100[/n]%[br]
📚 学业指数：[n]0-100[/n]%[br]
🎮 游戏指数：[n]0-100[/n]%[br]
[i]https://api.example.com/fortune?qq=[sqq]&date=[t]YYYYMMDD[/t][/i]
```

以上高级玩法结合了多种变量和API，实现了：
- 个性化抽签系统 🎯
- 群活跃度排行 📊
- 互动小游戏 🎮
- 表情包生成 😄
- 打卡排行榜 📋
- 真心话大冒险 🎲
- 运势预测 🔮

可以根据需要自由组合变量，打造更多有趣的功能！

## 注意事项 ⚠️

1. 变量嵌套最大深度为5层
2. API接口要选择稳定可靠的
3. 合理设置关键词匹配模式
4. 注意控制回复内容长度
5. 定期备份重要数据
6. 合理设置触发频率和延迟
7. 注意管理黑名单配置

## 常见问题 ❓

Q: 为什么API返回失败？
A: 检查API是否可用，网络是否正常

Q: 如何让回复更有趣？
A: 多用变量组合，加入表情符号

Q: 如何防止滥用？
A: 合理设置权限，限制使用频率