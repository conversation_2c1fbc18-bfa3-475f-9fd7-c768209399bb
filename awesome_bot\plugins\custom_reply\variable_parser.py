import re
import time
import random
import datetime
import json
import httpx
import asyncio
import os
from typing import Dict, List, Union, Optional, Callable, Any, Tuple, Set
from nonebot.adapters import Bot, Event, Message, MessageSegment
from nonebot.adapters.onebot.v11 import MessageSegment as V11MS
from nonebot.log import logger

class VariableParser:
    """变量解析器"""
    
    def __init__(self):
        self.variable_handlers = {}
        self.logger = logger.bind(name="custom_reply.variable")
        self._register_default_handlers()
    
    def _register_default_handlers(self):
        """注册默认变量处理器"""
        # 基础文本变量
        self.register_handler("br", self._handle_br)
        self.register_handler("space", self._handle_space)
        self.register_handler("n", self._handle_random_number)
        self.register_handler("in", self._handle_input)
        self.register_handler("IN", self._handle_raw_input)
        self.register_handler("t", self._handle_time)
        self.register_handler("rc", self._handle_recall)
        self.register_handler("rf", self._handle_random_face)
        self.register_handler("rt", self._handle_random_hitokoto)
        
        # 群组相关变量
        self.register_handler("gid", self._handle_group_id)
        self.register_handler("gnick", self._handle_group_nickname)
        self.register_handler("title", self._handle_group_title)
        self.register_handler("level", self._handle_group_level)
        self.register_handler("memcount", self._handle_member_count)
        self.register_handler("radmin", self._handle_random_admin)
        self.register_handler("owner", self._handle_group_owner)
        self.register_handler("gmc", self._handle_group_member_card)
        self.register_handler("jointime", self._handle_join_time)
        self.register_handler("lastmsg", self._handle_last_message)
        
        # 用户相关变量
        self.register_handler("sqq", self._handle_sender_qq)
        self.register_handler("bqq", self._handle_bot_qq)
        self.register_handler("tqq", self._handle_target_qq)
        self.register_handler("at", self._handle_at)
        self.register_handler("all", self._handle_at_all)
        self.register_handler("rpl", self._handle_reply)
        self.register_handler("pk", self._handle_poke)
        self.register_handler("b", self._handle_ban)
        self.register_handler("av", self._handle_avatar)
        self.register_handler("rgu", self._handle_random_group_user)
        
        # Cookie相关变量
        self.register_handler("bkn", self._handle_bkn)
        self.register_handler("skey", self._handle_skey)
        self.register_handler("p_skey", self._handle_p_skey)
        self.register_handler("cookie", self._handle_cookie)
        
        # 多媒体变量
        self.register_handler("image", self._handle_image)
        self.register_handler("xml", self._handle_xml)
        self.register_handler("json", self._handle_json)
        self.register_handler("music", self._handle_music)
        self.register_handler("record", self._handle_record)
        self.register_handler("video", self._handle_video)
        self.register_handler("rp", self._handle_redpacket)
        self.register_handler("location", self._handle_location)
        self.register_handler("link", self._handle_link)
        
        # 文本格式变量
        self.register_handler("code", self._handle_code)
        self.register_handler("table", self._handle_table)
        self.register_handler("font", self._handle_font)
        self.register_handler("color", self._handle_color)
        self.register_handler("b", self._handle_bold)
        self.register_handler("i", self._handle_italic)
        self.register_handler("u", self._handle_underline)
        self.register_handler("s", self._handle_strikethrough)
        
        # 游戏变量
        self.register_handler("di", self._handle_dice)
        self.register_handler("rps", self._handle_rock_paper_scissors)
        self.register_handler("rn", self._handle_random_choice)
        
        # 网络请求变量
        self.register_handler("g", self._handle_get_request)
        self.register_handler("p", self._handle_post_request)
        self.register_handler("url", self._handle_url_request)
        
        # 条件变量
        self.register_handler("if", self._handle_if_condition)
        
        # 文件操作变量
        self.register_handler("path", self._handle_path)
        self.register_handler("file", self._handle_file)
    
    def register_handler(self, var_name: str, handler: Callable):
        """注册变量处理器"""
        self.variable_handlers[var_name] = handler
    
    async def parse(self, text: str, bot: Bot, event: Event, extra_vars: Dict[str, Any] = None) -> str:
        """解析变量并返回处理后的文本"""
        if extra_vars is None:
            extra_vars = {}
        
        # 记录解析上下文，用于嵌套变量处理
        context = {
            "bot": bot,
            "event": event,
            "vars": extra_vars,
            "depth": 0,  # 当前嵌套深度
            "max_depth": 5  # 最大嵌套深度
        }
        
        # 处理变量
        return await self._process_text(text, context)
    
    async def _process_text(self, text: str, context: Dict[str, Any]) -> str:
        """处理文本中的变量"""
        # 如果超过最大嵌套深度，直接返回原文本
        if context["depth"] >= context["max_depth"]:
            return text
        
        # 变量匹配模式
        pattern = r'\[(.*?)\](.*?)(?:\[/\1\])?'
        
        # 处理所有匹配到的变量
        async def process_matches():
            result = text
            
            # 最多进行5轮处理，避免无限递归
            for _ in range(5):
                # 找出所有变量
                matches = re.finditer(pattern, result)
                changes = []
                
                for match in matches:
                    var_name = match.group(1)
                    var_content = match.group(2)
                    
                    # 处理嵌套变量
                    if re.search(pattern, var_content):
                        new_context = context.copy()
                        new_context["depth"] += 1
                        # 标记这是嵌套变量处理
                        if "vars" in new_context:
                            new_context["vars"]["in_nested"] = True
                        var_content = await self._process_text(var_content, new_context)
                    
                    # 处理变量
                    if var_name in self.variable_handlers:
                        try:
                            # 标记变量上下文
                            if "vars" in context:
                                context["vars"]["current_var"] = var_name
                                
                            # 调用处理器
                            replacement = await self.variable_handlers[var_name](
                                var_content, 
                                context["bot"], 
                                context["event"], 
                                context["vars"]
                            )
                            changes.append((match.start(), match.end(), replacement))
                            
                            # 清除变量上下文标记
                            if "vars" in context and "current_var" in context["vars"]:
                                del context["vars"]["current_var"]
                        except Exception as e:
                            self.logger.error(f"处理变量 [{var_name}] 出错: {str(e)}")
                            changes.append((match.start(), match.end(), f"[Error: {str(e)}]"))
                
                # 从后向前应用变更，避免索引错位
                changes.sort(reverse=True)
                for start, end, replacement in changes:
                    result = result[:start] + replacement + result[end:]
                
                # 如果没有变化，说明所有变量都已处理完毕
                if not changes:
                    break
            
            return result
        
        return await process_matches()
    
    # 基础文本变量处理器
    async def _handle_br(self, content: str, bot: Bot, event: Event, extra_vars: Dict[str, Any]) -> str:
        """处理换行符"""
        return "\n"
    
    async def _handle_space(self, content: str, bot: Bot, event: Event, extra_vars: Dict[str, Any]) -> str:
        """处理空格"""
        return " "
    
    async def _handle_random_number(self, content: str, bot: Bot, event: Event, extra_vars: Dict[str, Any]) -> str:
        """处理随机数"""
        try:
            start, end = map(int, content.split('-'))
            return str(random.randint(start, end))
        except Exception as e:
            self.logger.error(f"处理随机数变量出错: {str(e)}")
            return "[n格式错误]"
    
    async def _handle_input(self, content: str, bot: Bot, event: Event, extra_vars: Dict[str, Any]) -> str:
        """处理输入文本（转义）"""
        if "input_text" in extra_vars:
            return extra_vars["input_text"]
        return ""
    
    async def _handle_raw_input(self, content: str, bot: Bot, event: Event, extra_vars: Dict[str, Any]) -> str:
        """处理原始输入（不转义）"""
        if "raw_input_text" in extra_vars:
            return extra_vars["raw_input_text"]
        return ""
    
    async def _handle_time(self, content: str, bot: Bot, event: Event, extra_vars: Dict[str, Any]) -> str:
        """处理时间格式"""
        try:
            return datetime.datetime.now().strftime(content)
        except Exception as e:
            self.logger.error(f"处理时间变量出错: {str(e)}")
            return "[t格式错误]"
    
    async def _handle_recall(self, content: str, bot: Bot, event: Event, extra_vars: Dict[str, Any]) -> str:
        """处理撤回"""
        if not hasattr(event, "message_id"):
            return "[撤回失败:无消息ID]"
        
        # 将撤回标记添加到额外变量中，在实际发送消息后再执行撤回操作
        if "recall" not in extra_vars:
            extra_vars["recall"] = True
            extra_vars["recall_delay"] = int(content) if content.isdigit() else 0
        
        return ""
    
    async def _handle_random_face(self, content: str, bot: Bot, event: Event, extra_vars: Dict[str, Any]) -> str:
        """处理随机表情"""
        try:
            # 常见QQ表情ID
            face_ids = list(range(0, 221))  # QQ表情范围一般为0-221
            face_id = random.choice(face_ids)
            return str(V11MS.face(face_id))
        except Exception as e:
            self.logger.error(f"处理随机表情变量出错: {str(e)}")
            return "[表情]"
    
    async def _handle_random_hitokoto(self, content: str, bot: Bot, event: Event, extra_vars: Dict[str, Any]) -> str:
        """处理随机一言"""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get("https://v1.hitokoto.cn/?encode=text", timeout=5.0)
                if response.status_code == 200:
                    return response.text
                else:
                    return "[获取一言失败]"
        except Exception as e:
            self.logger.error(f"获取一言失败: {str(e)}")
            return "[获取一言失败]"
    
    # 群组相关变量处理器
    async def _handle_group_id(self, content: str, bot: Bot, event: Event, extra_vars: Dict[str, Any]) -> str:
        """处理群号"""
        if hasattr(event, "group_id"):
            return str(event.group_id)
        return "[非群聊]"
    
    async def _handle_group_nickname(self, content: str, bot: Bot, event: Event, extra_vars: Dict[str, Any]) -> str:
        """处理发送者群名片"""
        if not hasattr(event, "group_id") or not hasattr(event, "user_id"):
            return "[非群聊]"
        
        try:
            info = await bot.call_api(
                "get_group_member_info",
                group_id=event.group_id,
                user_id=event.user_id
            )
            return info.get("card", "") or info.get("nickname", "")
        except Exception as e:
            self.logger.error(f"获取群名片失败: {str(e)}")
            return "[获取群名片失败]"
    
    async def _handle_group_title(self, content: str, bot: Bot, event: Event, extra_vars: Dict[str, Any]) -> str:
        """处理发送者群头衔"""
        if not hasattr(event, "group_id") or not hasattr(event, "user_id"):
            return "[非群聊]"
        
        try:
            info = await bot.call_api(
                "get_group_member_info",
                group_id=event.group_id,
                user_id=event.user_id
            )
            return info.get("title", "")
        except Exception as e:
            self.logger.error(f"获取群头衔失败: {str(e)}")
            return "[获取群头衔失败]"
    
    async def _handle_group_level(self, content: str, bot: Bot, event: Event, extra_vars: Dict[str, Any]) -> str:
        """处理发送者群等级"""
        if not hasattr(event, "group_id") or not hasattr(event, "user_id"):
            return "[非群聊]"
        
        try:
            info = await bot.call_api(
                "get_group_member_info",
                group_id=event.group_id,
                user_id=event.user_id
            )
            return str(info.get("level", "0"))
        except Exception as e:
            self.logger.error(f"获取群等级失败: {str(e)}")
            return "[获取群等级失败]"
    
    async def _handle_member_count(self, content: str, bot: Bot, event: Event, extra_vars: Dict[str, Any]) -> str:
        """处理群成员数"""
        if not hasattr(event, "group_id"):
            return "[非群聊]"
        
        try:
            info = await bot.call_api(
                "get_group_info",
                group_id=event.group_id
            )
            return str(info.get("member_count", 0))
        except Exception as e:
            self.logger.error(f"获取群成员数失败: {str(e)}")
            return "[获取群成员数失败]"
    
    async def _handle_random_admin(self, content: str, bot: Bot, event: Event, extra_vars: Dict[str, Any]) -> str:
        """处理随机管理员"""
        if not hasattr(event, "group_id"):
            return "[非群聊]"
        
        try:
            member_list = await bot.call_api(
                "get_group_member_list",
                group_id=event.group_id
            )
            
            # 筛选管理员
            admins = [m for m in member_list if m.get("role") in ("admin", "owner")]
            
            if not admins:
                return "[无管理员]"
            
            admin = random.choice(admins)
            return str(admin.get("user_id", ""))
        except Exception as e:
            self.logger.error(f"获取随机管理员失败: {str(e)}")
            return "[获取随机管理员失败]"
    
    async def _handle_group_owner(self, content: str, bot: Bot, event: Event, extra_vars: Dict[str, Any]) -> str:
        """处理群主"""
        if not hasattr(event, "group_id"):
            return "[非群聊]"
        
        try:
            info = await bot.call_api(
                "get_group_info",
                group_id=event.group_id
            )
            return str(info.get("owner_id", 0))
        except Exception as e:
            self.logger.error(f"获取群主失败: {str(e)}")
            return "[获取群主失败]"
    
    async def _handle_group_member_card(self, content: str, bot: Bot, event: Event, extra_vars: Dict[str, Any]) -> str:
        """处理指定QQ的群名片"""
        if not hasattr(event, "group_id"):
            return "[非群聊]"
        
        try:
            qq = int(content)
            info = await bot.call_api(
                "get_group_member_info",
                group_id=event.group_id,
                user_id=qq
            )
            return info.get("card", "") or info.get("nickname", "")
        except Exception as e:
            self.logger.error(f"获取群名片失败: {str(e)}")
            return f"[获取{content}的群名片失败]"
    
    async def _handle_join_time(self, content: str, bot: Bot, event: Event, extra_vars: Dict[str, Any]) -> str:
        """处理入群时间"""
        if not hasattr(event, "group_id"):
            return "[非群聊]"
        
        try:
            qq = int(content)
            info = await bot.call_api(
                "get_group_member_info",
                group_id=event.group_id,
                user_id=qq
            )
            join_time = info.get("join_time", 0)
            if join_time:
                return datetime.datetime.fromtimestamp(join_time).strftime("%Y-%m-%d %H:%M:%S")
            return "[获取入群时间失败]"
        except Exception as e:
            self.logger.error(f"获取入群时间失败: {str(e)}")
            return f"[获取{content}的入群时间失败]"
    
    async def _handle_last_message(self, content: str, bot: Bot, event: Event, extra_vars: Dict[str, Any]) -> str:
        """处理最后发言时间"""
        if not hasattr(event, "group_id"):
            return "[非群聊]"
        
        try:
            qq = int(content)
            info = await bot.call_api(
                "get_group_member_info",
                group_id=event.group_id,
                user_id=qq
            )
            last_sent_time = info.get("last_sent_time", 0)
            if last_sent_time:
                return datetime.datetime.fromtimestamp(last_sent_time).strftime("%Y-%m-%d %H:%M:%S")
            return "[获取最后发言时间失败]"
        except Exception as e:
            self.logger.error(f"获取最后发言时间失败: {str(e)}")
            return f"[获取{content}的最后发言时间失败]"
    
    # 用户相关变量处理器
    async def _handle_sender_qq(self, content: str, bot: Bot, event: Event, extra_vars: Dict[str, Any]) -> str:
        """处理发送者QQ"""
        if hasattr(event, "user_id"):
            return str(event.user_id)
        return "[获取发送者QQ失败]"
    
    async def _handle_bot_qq(self, content: str, bot: Bot, event: Event, extra_vars: Dict[str, Any]) -> str:
        """处理机器人QQ"""
        # 根据上下文，这个变量可能有两种用法：
        # 1. 直接获取机器人QQ号: [bqq]
        # 2. 在嵌套情况下，作为参数传给其他变量: [at][bqq][/at]
        
        try:
            bot_id = str(bot.self_id)
            
            # 如果是在at变量中嵌套，前后会有特殊标记，此时返回纯数字
            if "vars" in extra_vars and extra_vars.get("in_nested", False):
                return bot_id
            
            # 如果用户可能想要直接@机器人，返回@CQ码
            if content.lower() == "at":
                at_segment = V11MS.at(bot_id)
                self.logger.info(f"创建@机器人消息段: {at_segment}")
                return str(at_segment)
            
            # 默认情况返回机器人QQ号
            return bot_id
        except Exception as e:
            self.logger.error(f"处理机器人QQ变量出错: {str(e)}")
            return "[获取机器人QQ失败]"
    
    async def _handle_target_qq(self, content: str, bot: Bot, event: Event, extra_vars: Dict[str, Any]) -> str:
        """处理被艾特者QQ"""
        try:
            # 尝试从消息中获取艾特的QQ号
            if hasattr(event, "message"):
                message = event.get_message()
                for segment in message:
                    if segment.type == "at" and "qq" in segment.data:
                        target_qq = segment.data["qq"]
                        
                        # 如果是在at变量中嵌套，返回纯数字
                        if extra_vars.get("in_nested", False):
                            return target_qq
                        
                        # 如果用户可能想要直接@被艾特者，返回@CQ码
                        if content.lower() == "at":
                            at_segment = V11MS.at(target_qq)
                            self.logger.info(f"创建@被艾特者消息段: {at_segment}")
                            return str(at_segment)
                        
                        # 默认返回被艾特者QQ号
                        return target_qq
            
            # 如果没有找到被艾特的对象
            return "[无艾特对象]"
        except Exception as e:
            self.logger.error(f"处理被艾特者QQ变量出错: {str(e)}")
            return "[获取被艾特者QQ失败]"
    
    async def _handle_at(self, content: str, bot: Bot, event: Event, extra_vars: Dict[str, Any]) -> str:
        """处理艾特"""
        try:
            # 处理空内容情况，默认@发送者
            if not content.strip() and hasattr(event, "user_id"):
                qq = str(event.user_id)
            else:
                qq = content.strip()
            
            # 创建@消息段
            at_segment = V11MS.at(qq)
            
            self.logger.info(f"创建@消息段: {at_segment}")
            return str(at_segment)
        except Exception as e:
            self.logger.error(f"处理艾特变量出错: {str(e)}")
            return f"@{content}"
    
    async def _handle_at_all(self, content: str, bot: Bot, event: Event, extra_vars: Dict[str, Any]) -> str:
        """处理艾特全体"""
        try:
            at_all_segment = V11MS.at("all")
            
            self.logger.info(f"创建@全体成员消息段: {at_all_segment}")
            return str(at_all_segment)
        except Exception as e:
            self.logger.error(f"处理艾特全体变量出错: {str(e)}")
            return "@全体成员"
    
    async def _handle_reply(self, content: str, bot: Bot, event: Event, extra_vars: Dict[str, Any]) -> str:
        """处理回复"""
        if not hasattr(event, "message_id"):
            return "[回复失败:无消息ID]"
        
        try:
            # 确保消息ID是字符串
            message_id = str(event.message_id)
            
            # 创建回复消息段并返回其字符串表示
            # 这会生成形如 [CQ:reply,id=123456] 的字符串
            reply_segment = V11MS.reply(message_id)
            
            self.logger.info(f"创建回复消息段: {reply_segment}")
            return str(reply_segment)
        except Exception as e:
            self.logger.error(f"处理回复变量出错: {str(e)}")
            return "[回复]"
    
    async def _handle_poke(self, content: str, bot: Bot, event: Event, extra_vars: Dict[str, Any]) -> str:
        """处理戳一戳"""
        try:
            return str(V11MS.poke(content))
        except Exception as e:
            self.logger.error(f"处理戳一戳变量出错: {str(e)}")
            return f"[戳一戳{content}]"
    
    async def _handle_ban(self, content: str, bot: Bot, event: Event, extra_vars: Dict[str, Any]) -> str:
        """处理禁言"""
        if not hasattr(event, "group_id"):
            return "[非群聊]"
        
        try:
            qq, duration = content.split('-')
            qq = qq.strip()
            duration = int(duration.strip()) * 60  # 转换为秒
            
            await bot.call_api(
                "set_group_ban",
                group_id=event.group_id,
                user_id=int(qq),
                duration=duration
            )
            
            return f"[已禁言{qq}]"
        except Exception as e:
            self.logger.error(f"处理禁言变量出错: {str(e)}")
            return f"[禁言失败:{str(e)}]"
    
    async def _handle_avatar(self, content: str, bot: Bot, event: Event, extra_vars: Dict[str, Any]) -> str:
        """处理头像"""
        try:
            qq = content.strip()
            return str(V11MS.image(f"https://q1.qlogo.cn/g?b=qq&nk={qq}&s=640"))
        except Exception as e:
            self.logger.error(f"处理头像变量出错: {str(e)}")
            return f"[头像获取失败]"
    
    async def _handle_random_group_user(self, content: str, bot: Bot, event: Event, extra_vars: Dict[str, Any]) -> str:
        """处理随机群友"""
        if not hasattr(event, "group_id"):
            return "[非群聊]"
        
        try:
            # 解析参数，获取随机抽取的数量
            count = 1
            if content:
                if '-' in content:
                    start, end = map(int, content.split('-'))
                    count = random.randint(start, end)
                else:
                    count = int(content)
            
            # 获取群成员列表
            member_list = await bot.call_api(
                "get_group_member_list",
                group_id=event.group_id
            )
            
            if not member_list:
                return "[获取群成员失败]"
            
            # 随机抽取指定数量的群友
            selected = random.sample(member_list, min(count, len(member_list)))
            
            # 构建结果
            result = []
            for member in selected:
                user_id = member.get("user_id", "")
                nickname = member.get("card", "") or member.get("nickname", "")
                result.append(f"{nickname}({user_id})")
            
            return "，".join(result)
        except Exception as e:
            self.logger.error(f"处理随机群友变量出错: {str(e)}")
            return f"[随机群友失败:{str(e)}]"
    
    # 多媒体变量处理器
    async def _handle_image(self, content: str, bot: Bot, event: Event, extra_vars: Dict[str, Any]) -> str:
        """处理图片"""
        try:
            return str(V11MS.image(content))
        except Exception as e:
            self.logger.error(f"处理图片变量出错: {str(e)}")
            return "[图片]"
    
    async def _handle_xml(self, content: str, bot: Bot, event: Event, extra_vars: Dict[str, Any]) -> str:
        """处理XML消息"""
        try:
            return str(V11MS.xml(content))
        except Exception as e:
            self.logger.error(f"处理XML变量出错: {str(e)}")
            return "[XML消息]"
    
    async def _handle_json(self, content: str, bot: Bot, event: Event, extra_vars: Dict[str, Any]) -> str:
        """处理JSON消息"""
        try:
            return str(V11MS.json(content))
        except Exception as e:
            self.logger.error(f"处理JSON变量出错: {str(e)}")
            return "[JSON消息]"
    
    async def _handle_music(self, content: str, bot: Bot, event: Event, extra_vars: Dict[str, Any]) -> str:
        """处理音乐分享"""
        try:
            # 默认使用QQ音乐
            return str(V11MS.music("qq", content))
        except Exception as e:
            self.logger.error(f"处理音乐变量出错: {str(e)}")
            return "[音乐分享]"
    
    async def _handle_record(self, content: str, bot: Bot, event: Event, extra_vars: Dict[str, Any]) -> str:
        """处理语音"""
        try:
            return str(V11MS.record(content))
        except Exception as e:
            self.logger.error(f"处理语音变量出错: {str(e)}")
            return "[语音]"
    
    async def _handle_video(self, content: str, bot: Bot, event: Event, extra_vars: Dict[str, Any]) -> str:
        """处理视频"""
        try:
            return str(V11MS.video(content))
        except Exception as e:
            self.logger.error(f"处理视频变量出错: {str(e)}")
            return "[视频]"
    
    # 游戏变量处理器
    async def _handle_dice(self, content: str, bot: Bot, event: Event, extra_vars: Dict[str, Any]) -> str:
        """处理骰子"""
        try:
            dice_value = 1
            if content and content.isdigit():
                dice_value = int(content)
                if dice_value < 1 or dice_value > 6:
                    dice_value = random.randint(1, 6)
            else:
                dice_value = random.randint(1, 6)
            
            return str(V11MS.dice(dice_value))
        except Exception as e:
            self.logger.error(f"处理骰子变量出错: {str(e)}")
            return f"[骰子:{dice_value}]"
    
    async def _handle_rock_paper_scissors(self, content: str, bot: Bot, event: Event, extra_vars: Dict[str, Any]) -> str:
        """处理猜拳"""
        try:
            rps_type = 0
            if content and content.isdigit():
                rps_type = int(content)
                if rps_type < 1 or rps_type > 3:
                    rps_type = random.randint(1, 3)
            else:
                rps_type = random.randint(1, 3)
            
            # 转换为 OneBot 的猜拳类型
            rps_map = {1: 0, 2: 1, 3: 2}  # 1石头2剪刀3布 -> 0石头1剪刀2布
            return str(V11MS.rps(rps_map[rps_type]))
        except Exception as e:
            self.logger.error(f"处理猜拳变量出错: {str(e)}")
            return f"[猜拳:{rps_type}]"
    
    async def _handle_random_choice(self, content: str, bot: Bot, event: Event, extra_vars: Dict[str, Any]) -> str:
        """处理随机选择"""
        try:
            options = [opt.strip() for opt in content.split(',') if opt.strip()]
            if not options:
                return "[随机选择:无选项]"
            return random.choice(options)
        except Exception as e:
            self.logger.error(f"处理随机选择变量出错: {str(e)}")
            return "[随机选择失败]"
    
    # 网络请求变量处理器
    async def _handle_get_request(self, content: str, bot: Bot, event: Event, extra_vars: Dict[str, Any]) -> str:
        """处理GET请求"""
        try:
            url = content.strip()
            async with httpx.AsyncClient() as client:
                response = await client.get(url, timeout=10.0)
                return response.text
        except Exception as e:
            self.logger.error(f"处理GET请求变量出错: {str(e)}")
            return f"[GET请求失败:{str(e)}]"
    
    async def _handle_post_request(self, content: str, bot: Bot, event: Event, extra_vars: Dict[str, Any]) -> str:
        """处理POST请求"""
        try:
            url, data_str = content.split('|', 1)
            url = url.strip()
            
            # 尝试解析JSON数据
            try:
                data = json.loads(data_str)
            except json.JSONDecodeError:
                # 如果不是JSON，尝试解析为表单数据
                data = {}
                for item in data_str.split('&'):
                    if '=' in item:
                        key, value = item.split('=', 1)
                        data[key.strip()] = value.strip()
            
            async with httpx.AsyncClient() as client:
                response = await client.post(url, json=data if isinstance(data, dict) else data_str, timeout=10.0)
                return response.text
        except Exception as e:
            self.logger.error(f"处理POST请求变量出错: {str(e)}")
            return f"[POST请求失败:{str(e)}]"
    
    async def _handle_url_request(self, content: str, bot: Bot, event: Event, extra_vars: Dict[str, Any]) -> str:
        """处理URL请求"""
        try:
            url = content.strip()
            async with httpx.AsyncClient() as client:
                response = await client.get(url, timeout=10.0)
                return response.text
        except Exception as e:
            self.logger.error(f"处理URL请求变量出错: {str(e)}")
            return f"[URL请求失败:{str(e)}]"
    
    # 条件变量处理器
    async def _handle_if_condition(self, content: str, bot: Bot, event: Event, extra_vars: Dict[str, Any]) -> str:
        """处理条件判断"""
        try:
            # 处理特殊条件语法: 表达式和内容部分
            if content.count('==') > 0:
                condition, value = content.split('==', 1)
                result = condition.strip() == value.strip()
            elif content.count('>') > 0:
                condition, value = content.split('>', 1)
                result = float(condition.strip()) > float(value.strip())
            elif content.count('<') > 0:
                condition, value = content.split('<', 1)
                result = float(condition.strip()) < float(value.strip())
            else:
                # 默认判断非空
                result = bool(content.strip())
            
            # 条件为真时返回内容，为假时返回空字符串
            return content if result else ""
        except Exception as e:
            self.logger.error(f"处理条件判断变量出错: {str(e)}")
            return f"[条件判断失败:{str(e)}]"
    
    # 文件操作变量处理器
    async def _handle_path(self, content: str, bot: Bot, event: Event, extra_vars: Dict[str, Any]) -> str:
        """处理文件路径"""
        try:
            path = content.strip()
            # 生成绝对路径（但不实际访问文件）
            abs_path = os.path.abspath(path)
            return abs_path
        except Exception as e:
            self.logger.error(f"处理文件路径变量出错: {str(e)}")
            return f"[获取路径失败:{str(e)}]"
    
    async def _handle_file(self, content: str, bot: Bot, event: Event, extra_vars: Dict[str, Any]) -> str:
        """处理文件内容"""
        try:
            path = content.strip()
            
            # 安全检查：限制在data目录下
            abs_path = os.path.abspath(path)
            if not abs_path.startswith(os.path.abspath("data")):
                return "[文件访问受限:只能访问data目录]"
            
            if not os.path.exists(abs_path):
                return f"[文件不存在:{path}]"
            
            import aiofiles
            async with aiofiles.open(abs_path, "r", encoding="utf-8") as f:
                content = await f.read()
                return content
        except Exception as e:
            self.logger.error(f"处理文件内容变量出错: {str(e)}")
            return f"[读取文件失败:{str(e)}]"

    # Cookie相关变量处理器
    async def _handle_bkn(self, content: str, bot: Bot, event: Event, extra_vars: Dict[str, Any]) -> str:
        """处理CSRF令牌"""
        # 由于无法直接获取CSRF令牌，返回提示信息
        return "[CSRF令牌功能受限]"
    
    async def _handle_skey(self, content: str, bot: Bot, event: Event, extra_vars: Dict[str, Any]) -> str:
        """处理SKEY"""
        # 由于无法直接获取SKEY，返回提示信息
        return "[SKEY功能受限]"
    
    async def _handle_p_skey(self, content: str, bot: Bot, event: Event, extra_vars: Dict[str, Any]) -> str:
        """处理P_SKEY"""
        # 由于无法直接获取P_SKEY，返回提示信息
        return "[P_SKEY功能受限]"
    
    async def _handle_cookie(self, content: str, bot: Bot, event: Event, extra_vars: Dict[str, Any]) -> str:
        """处理Cookie"""
        # 由于无法直接获取Cookie，返回提示信息
        return "[Cookie功能受限]"
    
    # 扩展多媒体变量处理器
    async def _handle_redpacket(self, content: str, bot: Bot, event: Event, extra_vars: Dict[str, Any]) -> str:
        """处理红包卡片"""
        try:
            parts = content.split(',', 1)
            if len(parts) != 2:
                return "[红包参数错误]"
            
            rp_type, title = parts
            # 这里只返回文本提示，因为实际发送红包需要特殊权限
            return f"[红包:{rp_type}:{title}]"
        except Exception as e:
            self.logger.error(f"处理红包变量出错: {str(e)}")
            return "[红包功能受限]"
    
    async def _handle_location(self, content: str, bot: Bot, event: Event, extra_vars: Dict[str, Any]) -> str:
        """处理位置分享"""
        try:
            parts = content.split(',', 2)
            if len(parts) != 3:
                return "[位置参数错误]"
            
            longitude, latitude, title = parts
            # 这里只返回文本提示，因为实际发送位置需要特殊消息段
            return f"[位置:{title}({longitude},{latitude})]"
        except Exception as e:
            self.logger.error(f"处理位置变量出错: {str(e)}")
            return "[位置分享功能受限]"
    
    async def _handle_link(self, content: str, bot: Bot, event: Event, extra_vars: Dict[str, Any]) -> str:
        """处理链接分享"""
        try:
            parts = content.split(',', 2)
            if len(parts) < 2:
                return "[链接参数错误]"
            
            url = parts[0].strip()
            title = parts[1].strip()
            image = parts[2].strip() if len(parts) > 2 else ""
            
            # 尝试构建一个文本形式的链接
            return f"{title}: {url}"
        except Exception as e:
            self.logger.error(f"处理链接变量出错: {str(e)}")
            return "[链接分享功能受限]"
    
    # 文本格式变量处理器
    async def _handle_code(self, content: str, bot: Bot, event: Event, extra_vars: Dict[str, Any]) -> str:
        """处理代码块"""
        try:
            parts = content.split(',', 1)
            if len(parts) != 2:
                return f"```\n{content}\n```"
            
            language, code = parts
            return f"```{language}\n{code}\n```"
        except Exception as e:
            self.logger.error(f"处理代码块变量出错: {str(e)}")
            return f"```\n{content}\n```"
    
    async def _handle_table(self, content: str, bot: Bot, event: Event, extra_vars: Dict[str, Any]) -> str:
        """处理表格"""
        # 简单返回文本，实际表格格式在富文本环境中才能展示
        return content
    
    async def _handle_font(self, content: str, bot: Bot, event: Event, extra_vars: Dict[str, Any]) -> str:
        """处理字体"""
        # 简单返回文本，因为普通消息无法设置字体
        return content
    
    async def _handle_color(self, content: str, bot: Bot, event: Event, extra_vars: Dict[str, Any]) -> str:
        """处理颜色"""
        # 简单返回文本，因为普通消息无法设置颜色
        return content
    
    async def _handle_bold(self, content: str, bot: Bot, event: Event, extra_vars: Dict[str, Any]) -> str:
        """处理粗体"""
        # 使用**加粗文本（Markdown语法）
        return f"**{content}**"
    
    async def _handle_italic(self, content: str, bot: Bot, event: Event, extra_vars: Dict[str, Any]) -> str:
        """处理斜体"""
        # 使用*斜体文本（Markdown语法）
        return f"*{content}*"
    
    async def _handle_underline(self, content: str, bot: Bot, event: Event, extra_vars: Dict[str, Any]) -> str:
        """处理下划线"""
        # 使用__下划线文本（Markdown语法）
        return f"__{content}__"
    
    async def _handle_strikethrough(self, content: str, bot: Bot, event: Event, extra_vars: Dict[str, Any]) -> str:
        """处理删除线"""
        # 使用~~删除线文本（Markdown语法）
        return f"~~{content}~~"

# 创建变量解析器实例
variable_parser = VariableParser() 