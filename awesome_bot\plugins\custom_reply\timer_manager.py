import asyncio
import datetime
import logging
import re
from typing import Dict, List, Optional, Set, Tuple, Any
from nonebot import get_bot, get_driver
from nonebot.adapters import Bo<PERSON>, Message
from nonebot.log import logger

from .data_manager import data_manager
from .variable_parser import variable_parser
from .message_builder import build_message

class TimerManager:
    """定时消息管理器"""
    
    def __init__(self):
        self.running = False
        self.tasks = set()
        self.scheduled_timers = {}  # 记录已经调度的定时器
        self.logger = logger.bind(name="custom_reply.timer")
        
    async def start(self):
        """启动定时任务"""
        self.running = True
        self.logger.info("定时消息管理器启动")
        
        while self.running:
            try:
                # 获取当前时间
                now = datetime.datetime.now()
                current_time = now.strftime("%H:%M")
                
                # 获取所有定时消息
                timers = await data_manager.get_all_timers()
                
                # 检查是否有定时消息需要发送
                for timer_key, timer_data in timers.items():
                    if timer_data.time == current_time and timer_key not in self.scheduled_timers:
                        # 记录已调度的定时器
                        self.scheduled_timers[timer_key] = now
                        
                        # 创建发送任务
                        self.logger.info(f"调度定时消息: {timer_data.name}")
                        task = asyncio.create_task(self._send_timer_message(timer_data))
                        self.tasks.add(task)
                        task.add_done_callback(self.tasks.discard)
                
                # 清理过期的调度记录
                current_minute = now.replace(second=0, microsecond=0)
                for key in list(self.scheduled_timers.keys()):
                    schedule_time = self.scheduled_timers[key]
                    if (current_minute - schedule_time.replace(second=0, microsecond=0)).total_seconds() >= 60:
                        del self.scheduled_timers[key]
                
                # 等待到下一分钟
                next_minute = now.replace(second=0, microsecond=0) + datetime.timedelta(minutes=1)
                sleep_seconds = (next_minute - now).total_seconds()
                await asyncio.sleep(sleep_seconds)
            
            except Exception as e:
                self.logger.error(f"定时任务出错: {str(e)}")
                await asyncio.sleep(60)  # 出错后等待一分钟再重试
    
    async def _send_timer_message(self, timer_data):
        """发送定时消息"""
        try:
            # 获取 Bot 实例
            bot = get_bot()
            
            # 解析变量
            try:
                # 创建模拟事件用于变量解析
                mock_event = self._create_mock_event(timer_data.chat_type, timer_data.chat_id)
                parsed_content = await variable_parser.parse(timer_data.content, bot, mock_event)
            except Exception as e:
                self.logger.error(f"解析变量失败: {str(e)}")
                parsed_content = timer_data.content
            
            # 构建消息
            message = await build_message(parsed_content, timer_data.image_ids)
            
            # 根据聊天类型发送消息
            if timer_data.chat_type == "group":
                await bot.call_api(
                    "send_group_msg",
                    group_id=timer_data.chat_id,
                    message=message
                )
                self.logger.info(f"定时消息已发送到群 {timer_data.chat_id}: {timer_data.name}")
            elif timer_data.chat_type == "private":
                await bot.call_api(
                    "send_private_msg",
                    user_id=timer_data.chat_id,
                    message=message
                )
                self.logger.info(f"定时消息已发送到用户 {timer_data.chat_id}: {timer_data.name}")
        except Exception as e:
            self.logger.error(f"发送定时消息失败: {str(e)}")
    
    def _create_mock_event(self, chat_type, chat_id):
        """创建模拟事件用于变量解析"""
        from nonebot.adapters.onebot.v11 import GroupMessageEvent, PrivateMessageEvent
        from nonebot.adapters.onebot.v11 import Message
        
        if chat_type == "group":
            # 创建模拟群消息事件
            return GroupMessageEvent(
                time=int(datetime.datetime.now().timestamp()),
                self_id=int(list(get_driver().bots.keys())[0]),
                post_type="message",
                sub_type="normal",
                user_id=int(list(get_driver().bots.keys())[0]),
                message_type="group",
                message_id=1,
                message=Message(""),
                original_message=Message(""),
                raw_message="",
                font=0,
                sender={
                    "user_id": int(list(get_driver().bots.keys())[0]),
                    "nickname": "Bot",
                },
                group_id=chat_id
            )
        else:
            # 创建模拟私聊消息事件
            return PrivateMessageEvent(
                time=int(datetime.datetime.now().timestamp()),
                self_id=int(list(get_driver().bots.keys())[0]),
                post_type="message",
                sub_type="friend",
                user_id=chat_id,
                message_type="private",
                message_id=1,
                message=Message(""),
                original_message=Message(""),
                raw_message="",
                font=0,
                sender={
                    "user_id": chat_id,
                    "nickname": "User",
                }
            )
    
    async def validate_time_format(self, time_str: str) -> bool:
        """验证时间格式
        
        Args:
            time_str: 时间字符串，格式为 "HH:MM"
            
        Returns:
            格式是否有效
        """
        pattern = r"^([01]?[0-9]|2[0-3]):([0-5][0-9])$"
        return bool(re.match(pattern, time_str))
    
    async def get_next_run_time(self, time_str: str) -> datetime.datetime:
        """获取下次运行时间
        
        Args:
            time_str: 时间字符串，格式为 "HH:MM"
            
        Returns:
            下次运行时间
        """
        hour, minute = map(int, time_str.split(":"))
        now = datetime.datetime.now()
        next_run = now.replace(hour=hour, minute=minute, second=0, microsecond=0)
        
        # 如果时间已过，则安排在明天同一时间
        if next_run <= now:
            next_run += datetime.timedelta(days=1)
            
        return next_run
    
    async def stop(self):
        """停止定时任务"""
        self.running = False
        
        # 取消所有任务
        for task in self.tasks:
            task.cancel()
        
        # 等待所有任务完成
        if self.tasks:
            await asyncio.gather(*self.tasks, return_exceptions=True)
            self.tasks.clear()
        
        self.logger.info("定时消息管理器已停止")

# 创建定时消息管理器实例
timer_manager = TimerManager()

# 注册启动和关闭事件
driver = get_driver()

@driver.on_startup
async def start_timer():
    # 加载数据
    await data_manager.load_data()
    
    # 启动定时任务
    asyncio.create_task(timer_manager.start())

@driver.on_shutdown
async def stop_timer():
    await timer_manager.stop() 